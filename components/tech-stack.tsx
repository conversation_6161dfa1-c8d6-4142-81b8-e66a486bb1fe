import { Card, CardContent } from "@/components/ui/card"
import {
  SiTypescript,
  SiNodedotjs,
  SiMongodb,
  SiPostgresql,
  SiGit,
  SiDocker,
  SiAmazon,
  SiGraphql,
  SiPython,
  SiFastapi,
  SiNestjs,
  SiNeo4J,
  SiRedis,
  SiLangchain
} from "react-icons/si"

// Define technologies
const technologies = [
  { name: "Python", icon: SiPython, color: "#3776AB" },
  { name: "FastAPI", icon: SiFastapi, color: "#009688" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", icon: SiLangchain, color: "#0000FF" },
  { name: "TypeScript", icon: SiTypescript, color: "#3178C6" },
  { name: "NestJS", icon: SiNestjs, color: "#E0234E" },
  { name: "MongoDB", icon: SiMongodb, color: "#47A248" },
  { name: "PostgreSQL", icon: SiPostgresql, color: "#4169E1" },
  { name: "Neo4j", icon: <PERSON><PERSON><PERSON>4<PERSON>, color: "#4581C3" },
  { name: "<PERSON><PERSON>", icon: SiRed<PERSON>, color: "#DC382D" },
  { name: "GraphQL", icon: SiGraphql, color: "#E10098" },
  { name: "Docker", icon: SiDocker, color: "#2496ED" },
  { name: "Node.js", icon: SiNodedotjs, color: "#339933" },
  { name: "AWS", icon: SiAmazon, color: "#FF9900" },
  { name: "Git", icon: SiGit, color: "#F05032" },
]

// Animate with staggered delay
const getAnimationDelay = (index: number) => {
  return `${(index % 8) * 0.1}s`;
};

export function TechStack() {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {technologies.map((tech, index) => {
        const Icon = tech.icon;
        return (
          <div 
            key={tech.name} 
            className="animate-fade-up" 
            style={{ animationDelay: getAnimationDelay(index) }}
          >
            <Card className="
              overflow-hidden glass-effect 
              hover-lift group transition-all duration-300
              border border-white/5
              h-full
            ">
              <CardContent className="p-5 flex flex-col items-center justify-center text-center h-full">
                <div className="
                  relative w-14 h-14 mb-3 
                  flex items-center justify-center 
                  p-3 rounded-full 
                  bg-secondary/30
                  group-hover:bg-primary/10
                  transition-all duration-300
                ">
                  <Icon 
                    className="
                      h-8 w-8 
                      transition-all duration-500 
                      group-hover:scale-110
                    " 
                    style={{ color: tech.color }} 
                  />
                  <div className="
                    absolute inset-0 opacity-0 
                    group-hover:opacity-100 
                    rounded-full 
                    transition-opacity duration-300
                    bg-gradient-to-r from-[rgba(var(--gradient-start),0.2)] to-[rgba(var(--gradient-end),0.1)]
                    animate-spin-slow
                    pointer-events-none
                  "></div>
                </div>
                <span className="
                  text-sm font-medium 
                  transition-colors duration-300
                  group-hover:text-primary
                  group-hover:text-glow
                ">{tech.name}</span>
              </CardContent>
            </Card>
          </div>
        );
      })}
    </div>
  );
}