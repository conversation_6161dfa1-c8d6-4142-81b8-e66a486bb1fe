"use client"

import { SessionProvider } from "next-auth/react"

export function AuthProvider({ 
  children,
  revalidateOnFocus = true
}: { 
  children: React.ReactNode,
  revalidateOnFocus?: boolean
}) {
  return (
    <SessionProvider 
      // Refetch session when window focuses to keep session fresh
      refetchOnWindowFocus={revalidateOnFocus}
      // Increase session polling interval to check more frequently (default 5min)
      refetchInterval={60} // Check every minute for session changes
    >
      {children}
    </SessionProvider>
  )
}