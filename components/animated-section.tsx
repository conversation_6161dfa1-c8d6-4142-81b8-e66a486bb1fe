"use client"

import type React from "react"
import { useRef, useEffect, useState } from "react"
import { cn } from "@/lib/utils"

interface AnimatedSectionProps {
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
  animation?: 'fade' | 'slide-up' | 'slide-down' | 'slide-left' | 'slide-right' | 'zoom' | 'rotate'
  delay?: number
  duration?: number
  threshold?: number
  once?: boolean
  hoverEffect?: boolean
  parallax?: boolean
}

export function AnimatedSection({ 
  children, 
  className, 
  style,
  animation = 'fade',
  delay = 0,
  duration = 1000,
  threshold = 0.1,
  once = true,
  hoverEffect = false,
  parallax = false
}: AnimatedSectionProps) {
  const sectionRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          if (once) {
            observer.unobserve(entry.target)
          }
        } else if (!once) {
          setIsVisible(false)
        }
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: threshold,
      },
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [once, threshold])

  // Handle mouse movement for parallax effect
  useEffect(() => {
    if (!parallax) return

    const handleMouseMove = (e: MouseEvent) => {
      if (sectionRef.current) {
        const rect = sectionRef.current.getBoundingClientRect()
        const x = e.clientX - rect.left - rect.width / 2
        const y = e.clientY - rect.top - rect.height / 2
        setMousePosition({ x, y })
      }
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [parallax])

  // Generate animation classes based on props
  const getAnimationClasses = () => {
    if (!isVisible) return 'opacity-0'
    
    const baseClasses = ['opacity-100']
    
    switch (animation) {
      case 'slide-up':
        baseClasses.push('animate-fade-up')
        break
      case 'slide-down':
        baseClasses.push('animate-fade-down')
        break
      case 'slide-left':
        baseClasses.push('animate-fade-left')
        break
      case 'slide-right':
        baseClasses.push('animate-fade-right')
        break
      case 'zoom':
        baseClasses.push('scale-100')
        break
      case 'rotate':
        baseClasses.push('rotate-0')
        break
      default:
        // Default fade animation
        break
    }
    
    return baseClasses.join(' ')
  }

  // Generate initial classes for animations
  const getInitialClasses = () => {
    const baseClasses = ['opacity-0']
    
    switch (animation) {
      case 'slide-up':
        return 'transform translate-y-10 opacity-0'
      case 'slide-down':
        return 'transform -translate-y-10 opacity-0'
      case 'slide-left':
        return 'transform translate-x-10 opacity-0'
      case 'slide-right':
        return 'transform -translate-x-10 opacity-0'
      case 'zoom':
        return 'transform scale-95 opacity-0'
      case 'rotate':
        return 'transform rotate-12 opacity-0'
      default:
        return 'opacity-0'
    }
  }

  // Generate transition style
  const getTransitionStyle = () => {
    return {
      transitionProperty: 'opacity, transform',
      transitionDuration: `${duration}ms`,
      transitionDelay: `${delay}ms`,
      transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
      ...(parallax && {
        transform: `perspective(1000px) rotateX(${mousePosition.y * 0.01}deg) rotateY(${-mousePosition.x * 0.01}deg)`,
      }),
      ...style
    }
  }

  // Combine classes
  const combinedClasses = cn(
    "transition-all",
    isVisible ? getAnimationClasses() : getInitialClasses(),
    hoverEffect && "hover:shadow-xl hover:-translate-y-1 hover:scale-[1.02]",
    className
  )

  return (
    <section
      ref={sectionRef}
      className={combinedClasses}
      style={getTransitionStyle()}
    >
      {children}
    </section>
  )
}

