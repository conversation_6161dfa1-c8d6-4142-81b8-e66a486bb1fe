"use client"

import Link from "next/link"
import { Brush, Menu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON>Trigger } from "@/components/ui/sheet"

export default function MobileMenu() {
  return (
    <div className="block md:hidden">
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="focus:ring-0">
            <Menu className="h-5 w-5 text-primary" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-[75%] sm:w-[350px] glass-effect border-white/10">
          <div className="flex flex-col gap-6 mt-8">
            <Link href="/" className="text-lg font-medium group flex items-center gap-2">
              <span className="text-glow transition-all duration-300 group-hover:text-primary">Home</span>
            </Link>
            <Link href="/about" className="text-lg font-medium group flex items-center gap-2">
              <span className="text-glow transition-all duration-300 group-hover:text-primary">About</span>
            </Link>
            <Link href="/blog" className="text-lg font-medium group flex items-center gap-2">
              <span className="text-glow transition-all duration-300 group-hover:text-primary">Blog</span>
            </Link>
            <Link href="/projects" className="text-lg font-medium group flex items-center gap-2">
              <span className="text-glow transition-all duration-300 group-hover:text-primary">Projects</span>
            </Link>
            <Link href="/contact" className="text-lg font-medium group flex items-center gap-2">
              <span className="text-glow transition-all duration-300 group-hover:text-primary">Contact</span>
            </Link>
            <div className="border-t border-white/10 pt-4 mt-2">
              <Link href="/paint" className="text-lg font-medium group flex items-center gap-2">
                <Brush className="h-4 w-4 text-primary animate-waving" />
                <span className="text-glow transition-all duration-300 group-hover:text-primary">Paint</span>
              </Link>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}