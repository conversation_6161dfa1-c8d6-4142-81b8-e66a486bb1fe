"use client"

import Link from "next/link"
import { <PERSON>, <PERSON>u, Brush } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, She<PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"

export function SiteHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b glass-effect transition-all duration-200 ease-in-out border-white/10">
      <div className="container flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center gap-2 font-bold text-xl hover-lift">
          <div className="p-2 rounded-full bg-primary/10 animate-pulse mr-1">
            <Code className="h-5 w-5 text-primary" />
          </div>
          <span className="gradient-text">DevPortfolio</span>
        </Link>
        
        <nav className="hidden md:flex gap-8" aria-label="Main Navigation">
          <Link href="/" className="text-sm font-medium relative group">
            <span className="text-glow transition-all duration-300 group-hover:text-primary">Home</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
          </Link>
          <Link href="/about" className="text-sm font-medium relative group">
            <span className="text-glow transition-all duration-300 group-hover:text-primary">About</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
          </Link>
          <Link href="/blog" className="text-sm font-medium relative group">
            <span className="text-glow transition-all duration-300 group-hover:text-primary">Blog</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
          </Link>
          <Link href="/projects" className="text-sm font-medium relative group">
            <span className="text-glow transition-all duration-300 group-hover:text-primary">Projects</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
          </Link>
          {/* TODO: Comment this out to hide contact on header menu */}
          <Link href="/contact" className="text-sm font-medium relative group">
            <span className="text-glow transition-all duration-300 group-hover:text-primary">Contact</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
          </Link>
        </nav>
        
        <div className="flex items-center gap-4">
          {/* Mobile burger menu */}
          <div className="block md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="focus:ring-0">
                  <Menu className="h-5 w-5 text-primary" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[75%] sm:w-[350px] glass-effect border-white/10">
                <div className="flex flex-col gap-6 mt-8">
                  <Link href="/" className="text-lg font-medium group flex items-center gap-2">
                    <span className="text-glow transition-all duration-300 group-hover:text-primary">Home</span>
                  </Link>
                  <Link href="/about" className="text-lg font-medium group flex items-center gap-2">
                    <span className="text-glow transition-all duration-300 group-hover:text-primary">About</span>
                  </Link>
                  <Link href="/blog" className="text-lg font-medium group flex items-center gap-2">
                    <span className="text-glow transition-all duration-300 group-hover:text-primary">Blog</span>
                  </Link>
                  <Link href="/projects" className="text-lg font-medium group flex items-center gap-2">
                    <span className="text-glow transition-all duration-300 group-hover:text-primary">Projects</span>
                  </Link>
                  <Link href="/contact" className="text-lg font-medium group flex items-center gap-2">
                    <span className="text-glow transition-all duration-300 group-hover:text-primary">Contact</span>
                  </Link>
                  <div className="border-t border-white/10 pt-4 mt-2">
                    <Link href="/paint" className="text-lg font-medium group flex items-center gap-2">
                      <Brush className="h-4 w-4 text-primary animate-waving" />
                      <span className="text-glow transition-all duration-300 group-hover:text-primary">Paint</span>
                    </Link>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
          
          {/* Desktop paint button */}
          <Button variant="outline" size="sm" asChild className="hidden md:flex hover-lift gradient-border">
            <Link href="/paint" className="px-4 py-2 flex items-center gap-2">
              <Brush className="h-4 w-4 text-primary animate-waving" />
              <span className="text-glow">Paint</span>
            </Link>
          </Button>
        </div>
      </div>
    </header>
  )
}