"use client"

import { useState, useEffect } from 'react';

interface TypeWriterProps {
  phrases: string[];
  typingSpeed?: number;
  deletingSpeed?: number;
  delayBetween?: number;
  colorful?: boolean;
  className?: string;
}

export function TypeWriter({ 
  phrases, 
  typingSpeed = 50, 
  deletingSpeed = 30, 
  delayBetween = 2000,
  colorful = true,
  className = ""
}: TypeWriterProps) {
  const [currentPhrase, setCurrentPhrase] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    // If no phrases, exit early
    if (phrases.length === 0 || isPaused) return;
    
    let timer: NodeJS.Timeout;
    
    if (isDeleting) {
      // Handle deletion
      timer = setTimeout(() => {
        if (currentPhrase.length === 0) {
          // Complete deletion - move to next phrase after a pause
          setIsDeleting(false);
          setIsPaused(true);
          setTimeout(() => {
            setCurrentIndex((prevIndex) => (prevIndex + 1) % phrases.length);
            setIsPaused(false);
          }, 700);
        } else {
          // Continue deleting
          setCurrentPhrase(current => current.slice(0, -1));
        }
      }, deletingSpeed);
    } else {
      // Handle typing
      const fullPhrase = phrases[currentIndex];
      
      timer = setTimeout(() => {
        if (currentPhrase.length >= fullPhrase.length) {
          // Complete typing - wait before deleting
          setTimeout(() => setIsDeleting(true), delayBetween);
        } else {
          // Continue typing
          setCurrentPhrase(current => fullPhrase.slice(0, current.length + 1));
        }
      }, typingSpeed);
    }
    
    return () => clearTimeout(timer);
  }, [currentPhrase, currentIndex, isDeleting, isPaused, phrases, typingSpeed, deletingSpeed, delayBetween]);

  return (
    <span className={className}>
      <span className={`${colorful ? 'text-primary' : 'text-foreground'} font-semibold text-xl md:text-2xl`}>
        {currentPhrase}
        <span className="inline-block ml-0.5 animate-blink">|</span>
      </span>
      <style jsx>{`
        @keyframes blink {
          0%, 100% { opacity: 1; }
          50% { opacity: 0; }
        }
        .animate-blink {
          animation: blink 1s step-end infinite;
        }
      `}</style>
    </span>
  );
}
