"use client"

import { useRef, useEffect } from "react"
import { ChevronLeft, ChevronRight, Calendar, Star, Code, GitBranch, Layers } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AnimatedSection } from "@/components/animated-section"
import { Card, CardContent } from "@/components/ui/card"

interface Testimonial {
  name: string
  company: string
  image: string
  text: string
  rating: number
}

interface Company {
  name: string
  logo: string
  period: string
  role: string
}

interface ExperienceSectionProps {
  experienceYears: number
  testimonials: Testimonial[]
  companies: Company[]
}

export function ExperienceSection({
  experienceYears,
  testimonials,
  companies
}: ExperienceSectionProps) {
  const testimonialsRef = useRef<HTMLDivElement>(null)
  const companiesRef = useRef<HTMLDivElement>(null)

  const scrollTestimonials = (direction: "left" | "right") => {
    if (testimonialsRef.current) {
      const { scrollLeft, clientWidth } = testimonialsRef.current
      const scrollTo = direction === "left" 
        ? scrollLeft - clientWidth * 0.8 
        : scrollLeft + clientWidth * 0.8
      
      testimonialsRef.current.scrollTo({
        left: scrollTo,
        behavior: "smooth"
      })
    }
  }

  const scrollCompanies = (direction: "left" | "right") => {
    if (companiesRef.current) {
      const { scrollLeft, clientWidth } = companiesRef.current
      const scrollTo = direction === "left" 
        ? scrollLeft - clientWidth * 0.8 
        : scrollLeft + clientWidth * 0.8
      
      companiesRef.current.scrollTo({
        left: scrollTo,
        behavior: "smooth"
      })
    }
  }

  return (
    <AnimatedSection className="w-full py-28 md:py-32 lg:py-36 bg-muted/10 border-y border-muted/30">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-8 text-center mb-20">
          <div className="space-y-4 max-w-3xl">
            <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm animate-fade-up">Experience</div>
            <h2
              className="text-3xl font-bold tracking-tighter md:text-4xl animate-fade-up"
              style={{ animationDelay: "100ms" }}
            >
              My Professional Journey
            </h2>
            <p
              className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-up"
              style={{ animationDelay: "200ms" }}
            >
              A track record of successful projects and innovative solutions
            </p>
          </div>
        </div>
        
        {/* Stats Section */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-24 animate-fade-up" style={{ animationDelay: "300ms" }}>
          <Card className="shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 overflow-hidden group">
            <CardContent className="p-6 flex flex-col items-center justify-center space-y-2 text-center">
              <Code className="h-8 w-8 mb-2 text-primary group-hover:scale-110 transition-transform duration-300" />
              <div className="text-4xl font-bold">10+</div>
              <p className="text-muted-foreground">AI Solutions</p>
              <div className="h-1 w-12 bg-primary/30 mt-1 group-hover:w-16 transition-all duration-300"></div>
            </CardContent>
          </Card>
          
          <Card className="shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 overflow-hidden group">
            <CardContent className="p-6 flex flex-col items-center justify-center space-y-2 text-center">
              <Calendar className="h-8 w-8 mb-2 text-primary group-hover:scale-110 transition-transform duration-300" />
              <div className="text-4xl font-bold">{experienceYears}+</div>
              <p className="text-muted-foreground">Years Experience</p>
              <div className="h-1 w-12 bg-primary/30 mt-1 group-hover:w-16 transition-all duration-300"></div>
            </CardContent>
          </Card>
          
          <Card className="shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 overflow-hidden group">
            <CardContent className="p-6 flex flex-col items-center justify-center space-y-2 text-center">
              <GitBranch className="h-8 w-8 mb-2 text-primary group-hover:scale-110 transition-transform duration-300" />
              <div className="text-4xl font-bold">300+</div>
              <p className="text-muted-foreground">PR Merged</p>
              <div className="h-1 w-12 bg-primary/30 mt-1 group-hover:w-16 transition-all duration-300"></div>
            </CardContent>
          </Card>
          
          <Card className="shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300 overflow-hidden group">
            <CardContent className="p-6 flex flex-col items-center justify-center space-y-2 text-center">
              <Layers className="h-8 w-8 mb-2 text-primary group-hover:scale-110 transition-transform duration-300" />
              <div className="text-4xl font-bold">15+</div>
              <p className="text-muted-foreground">Microservices</p>
              <div className="h-1 w-12 bg-primary/30 mt-1 group-hover:w-16 transition-all duration-300"></div>
            </CardContent>
          </Card>
        </div>
        
        {/* Testimonials Section - Commented Out
        <div className="mb-24">
          <div className="flex justify-between items-center mb-8">
            <h3 className="text-2xl font-bold">Testimonials</h3>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="icon" 
                onClick={() => scrollTestimonials("left")}
                className="rounded-full"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                onClick={() => scrollTestimonials("right")}
                className="rounded-full"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          <div 
            ref={testimonialsRef}
            className="flex overflow-x-auto space-x-6 pb-6 hide-scrollbar snap-x snap-mandatory"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none'
            }}
          >
            {testimonials.map((testimonial, index) => (
              <div 
                key={index}
                className="flex-none w-full sm:w-[calc(100%-40px)] md:w-[calc(50%-20px)] lg:w-[calc(33.333%-20px)] snap-start"
              >
                <Card className="h-full shadow-md hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="mb-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className={`inline-block h-4 w-4 ${i < testimonial.rating ? 'text-yellow-500 fill-yellow-500' : 'text-muted'}`} />
                      ))}
                    </div>
                    <blockquote className="text-lg italic mb-6 flex-grow">
                      "{testimonial.text}"
                    </blockquote>
                    <div className="flex items-center mt-auto">
                      <div className="h-12 w-12 rounded-full overflow-hidden mr-4">
                        <img 
                          src={testimonial.image} 
                          alt={testimonial.name} 
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div>
                        <div className="font-medium">{testimonial.name}</div>
                        <div className="text-sm text-muted-foreground">{testimonial.company}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
        */}
        
        {/* Companies Section - Commented Out
        <div>
          <div className="flex justify-between items-center mb-8">
            <h3 className="text-2xl font-bold">Teams & Projects:</h3>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="icon" 
                onClick={() => scrollCompanies("left")}
                className="rounded-full"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                onClick={() => scrollCompanies("right")}
                className="rounded-full"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
          
          <div 
            ref={companiesRef}
            className="flex overflow-x-auto space-x-6 pb-6 hide-scrollbar snap-x snap-mandatory"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none'
            }}
          >
            {companies.map((company, index) => (
              <div 
                key={index}
                className="flex-none w-[250px] snap-start"
              >
                <Card className="h-full shadow-md hover:shadow-lg hover:border-primary/30 transition-all duration-300 group">
                  <CardContent className="p-6 flex flex-col items-center text-center h-full">
                    <div className="bg-muted/50 p-4 rounded-lg mb-4 w-20 h-20 flex items-center justify-center group-hover:bg-muted/70 transition-colors">
                      <img 
                        src={company.logo} 
                        alt={company.name} 
                        className="max-h-12 max-w-12"
                      />
                    </div>
                    <div className="font-medium mb-1">{company.name}</div>
                    <Badge variant="secondary" className="mb-2">{company.role}</Badge>
                    <div className="text-sm text-muted-foreground">{company.period}</div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
        */}
      </div>
      <style jsx global>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </AnimatedSection>
  )
}