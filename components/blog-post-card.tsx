import Link from "next/link"
import { Calendar, ArrowUpRight, Bookmark } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON>Footer, <PERSON>Header, CardTitle } from "@/components/ui/card"

interface BlogPostCardProps {
  title: string
  excerpt: string
  date: string
  tags: string[]
  slug: string
  featured?: boolean
  coverImage?: string
}

export function BlogPostCard({ 
  title, 
  excerpt, 
  date, 
  tags, 
  slug, 
  featured = false,
  coverImage
}: BlogPostCardProps) {
  return (
    <Link href={`/blog/${slug}`} className="h-full">
      <Card className={`
        h-full flex flex-col hover-lift 
        glass-effect group transition-all duration-500
        border border-white/5
        ${featured ? 'gradient-border' : ''}
      `}>
        <div className="aspect-video overflow-hidden relative">
          <img
            src={coverImage || "/placeholder.svg"}
            alt={title}
            width={400}
            height={225}
            className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background/90 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end justify-end p-4">
            <div className="p-2 rounded-full bg-primary/20 backdrop-blur-sm">
              <Bookmark className="h-5 w-5 text-primary" />
            </div>
          </div>
          {featured && (
            <div className="absolute top-3 right-3">
              <Badge variant="secondary" className="bg-primary/20 backdrop-blur-sm text-primary-foreground px-3 py-1">
                Featured
              </Badge>
            </div>
          )}
        </div>
        <CardHeader>
          <CardTitle className="line-clamp-2 group-hover:text-primary transition-colors text-xl md:text-2xl flex items-center gap-2">
            <span className={featured ? 'gradient-text' : ''}>{title}</span>
            <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
          </CardTitle>
          <div className="flex items-center text-sm text-muted-foreground mt-2">
            <div className="p-1.5 rounded-full bg-secondary/30 mr-2">
              <Calendar className="h-3.5 w-3.5 text-primary" />
            </div>
            {date}
          </div>
        </CardHeader>
        <CardContent className="flex-grow">
          <p className="text-muted-foreground line-clamp-3">{excerpt}</p>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2 border-t border-white/10 pt-4">
          {tags.map((tag, i) => (
            <Badge
              key={i}
              variant="secondary"
              className="transition-all duration-300 hover:bg-primary/20 hover:text-primary glass-effect"
            >
              {tag}
            </Badge>
          ))}
        </CardFooter>
      </Card>
    </Link>
  )
}

