"use client"

import { <PERSON><PERSON>caseIcon, GraduationCap, Medal, CalendarIcon, CheckCircle2, FileTextIcon, ExternalLinkIcon, DownloadIcon } from "lucide-react"
import { AnimatedSection } from "@/components/animated-section"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface WorkExperience {
  company: string
  position: string
  duration: string
  location: string
  description: string
  achievements?: string[]
  projects?: string[]
  technologies?: string[]
}

interface Education {
  institution: string
  degree: string
  duration: string
  location: string
  description?: string
}

interface AboutSectionProps {
  name: string
  title: string
  bio: string
  workExperience: WorkExperience[]
  education: Education[]
  resumeLink?: string
  showResume?: boolean
}

export function AboutSection({
  name,
  title,
  bio,
  workExperience,
  education,
  resumeLink,
  showResume = false
}: AboutSectionProps) {
  return (
    <AnimatedSection className="w-full py-28 md:py-32 lg:py-36 bg-muted/5 border-b border-muted/30">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-8 text-center mb-16">
          <div className="space-y-4 max-w-3xl">
            <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm animate-fade-up">About Me</div>
            <h2
              className="text-3xl font-bold tracking-tighter md:text-4xl animate-fade-up"
              style={{ animationDelay: "100ms" }}
            >
              My Personal Journey
            </h2>
            <p
              className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-up"
              style={{ animationDelay: "200ms" }}
            >
              {bio}
            </p>
          </div>
        </div>

        <Tabs defaultValue="experience" className="w-full max-w-4xl mx-auto">
          <TabsList className="grid w-full grid-cols-[repeat(auto-fit,minmax(0,1fr))] mb-12">
            <TabsTrigger value="experience" className="text-base py-3">Work Experience</TabsTrigger>
            <TabsTrigger value="education" className="text-base py-3">Education</TabsTrigger>
            {showResume && <TabsTrigger value="resume" className="text-base py-3">Resume</TabsTrigger>}
          </TabsList>

          <TabsContent value="experience" className="animate-fade-up">
            <div className="space-y-8">
              {workExperience.map((job, index) => (
                <Card key={index} className="hover:shadow-md transition-all duration-300 overflow-hidden border-muted/40 group hover:border-primary/30">
                  <CardContent className="p-6 md:p-8">
                    <div className="flex flex-col md:flex-row md:items-start gap-6">
                      <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                        <BriefcaseIcon className="h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      
                      <div className="flex-grow space-y-4">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                          <div>
                            <h3 className="text-xl font-bold">{job.position}</h3>
                            <p className="text-lg text-muted-foreground">{job.company}</p>
                          </div>
                          <div className="flex items-center gap-2 text-muted-foreground text-sm">
                            <CalendarIcon className="h-4 w-4" />
                            <span>{job.duration}</span>
                            <span>•</span>
                            <span>{job.location}</span>
                          </div>
                        </div>
                        
                        <p className="text-muted-foreground">{job.description}</p>
                        
                        {job.achievements && job.achievements.length > 0 && (
                          <div className="space-y-2 pt-2">
                            <h4 className="font-medium">Key Achievements:</h4>
                            <ul className="space-y-1">
                              {job.achievements.map((achievement, i) => (
                                <li key={i} className="flex items-start gap-2">
                                  <CheckCircle2 className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                                  <span>{achievement}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {job.projects && job.projects.length > 0 && (
                          <div className="space-y-2 pt-2">
                            <h4 className="font-medium">Notable Projects:</h4>
                            <ul className="space-y-1">
                              {job.projects.map((project, i) => (
                                <li key={i} className="flex items-start gap-2">
                                  <CheckCircle2 className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                                  <span>{project}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {job.technologies && job.technologies.length > 0 && (
                          <div className="flex flex-wrap gap-2 pt-3">
                            {job.technologies.map((tech, i) => (
                              <Badge key={i} variant="secondary">{tech}</Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="education" className="animate-fade-up">
            <div className="space-y-8">
              {education.map((edu, index) => (
                <Card key={index} className="hover:shadow-md transition-all duration-300 overflow-hidden border-muted/40 group hover:border-primary/30">
                  <CardContent className="p-6 md:p-8">
                    <div className="flex flex-col md:flex-row md:items-start gap-6">
                      <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                        <GraduationCap className="h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      
                      <div className="flex-grow space-y-4">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                          <div>
                            <h3 className="text-xl font-bold">{edu.degree}</h3>
                            <p className="text-lg text-muted-foreground">{edu.institution}</p>
                          </div>
                          <div className="flex items-center gap-2 text-muted-foreground text-sm">
                            <CalendarIcon className="h-4 w-4" />
                            <span>{edu.duration}</span>
                            <span>•</span>
                            <span>{edu.location}</span>
                          </div>
                        </div>
                        
                        {edu.description && (
                          <p className="text-muted-foreground">{edu.description}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="resume" className="animate-fade-up">
            {showResume && resumeLink ? (
              <Card className="hover:shadow-md transition-all duration-300 overflow-hidden border-muted/40 group hover:border-primary/30">
                <CardContent className="p-6 md:p-8">
                  <div className="flex flex-col items-center text-center space-y-6">
                    <div className="flex-shrink-0 flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 text-primary">
                      <FileTextIcon className="h-8 w-8 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    
                    <div className="space-y-3">
                      <h3 className="text-xl font-bold">My Resume</h3>
                      <p className="text-muted-foreground max-w-2xl">
                        View or download my complete resume with detailed information about my work experience, education, skills, and achievements.
                      </p>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <Button asChild className="gap-2">
                        <a href={resumeLink} target="_blank" rel="noopener noreferrer">
                          <ExternalLinkIcon className="h-4 w-4" />
                          View Resume
                        </a>
                      </Button>
                      {/* <Button variant="outline" asChild className="gap-2">
                        <a href={resumeLink} download="Resume_Kartik_Jain.pdf">
                          <DownloadIcon className="h-4 w-4" />
                          Download Resume
                        </a>
                      </Button> */}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="flex justify-center items-center py-16">
                <p className="text-muted-foreground">Resume is currently unavailable.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </AnimatedSection>
  )
}