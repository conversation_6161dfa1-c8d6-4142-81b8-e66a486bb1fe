"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import Link from "next/link"
import { ArrowLeft, Save, Trash2, Mail, Globe, Shield, Bell, Smartphone } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"

export default function AdminSettings() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  
  // Site settings
  const [siteTitle, setSiteTitle] = useState("<PERSON><PERSON><PERSON>")
  const [siteDescription, setSiteDescription] = useState("A personal website showcasing blogs and portfolio")
  const [footerText, setFooterText] = useState("© 2025 Kartik Jain. All rights reserved.")
  const [showExperienceSection, setShowExperienceSection] = useState(true)
  
  // Social media settings
  const [socialLinks, setSocialLinks] = useState({
    github: "https://github.com/kartikjain-42",
    twitter: "https://twitter.com/kartikjain42",
    linkedin: "https://linkedin.com/in/kartikjain42"
  })
  
  // Email settings
  const [emailSettings, setEmailSettings] = useState({
    notifyOnContact: true,
    notifyOnSubscription: true,
    adminEmail: "<EMAIL>",
    replyToEmail: "<EMAIL>"
  })
  
  // Analytics settings
  const [analyticsSettings, setAnalyticsSettings] = useState({
    enableTracking: true,
    anonymizeIp: true,
    trackOutboundLinks: true,
    googleAnalyticsId: "G-XXXXXXXXXX",
    excludeAdminFromAnalytics: true
  })
  
  // Security settings
  const [securitySettings, setSecuritySettings] = useState({
    enableTwoFactor: false,
    sessionTimeout: "30",
    allowedIpAddresses: "",
    enableBruteForceProtection: true,
    maxLoginAttempts: "5",
    allowedEmails: "<EMAIL>,<EMAIL>"
  })
  
  // Mobile settings
  const [mobileSettings, setMobileSettings] = useState({
    enablePwa: true,
    enableNotifications: false,
    allowInstallPrompt: true,
    cacheStrategy: "network-first"
  })

  const saveSettings = async (section: string) => {
    setLoading(true)
    
    // Simulate API call to save settings
    setTimeout(() => {
      setLoading(false)
      toast.success(`${section} settings saved successfully`)
    }, 1000)
  }
  
  const handleInputChange = (section: string, field: string, value: any) => {
    switch (section) {
      case "social":
        setSocialLinks(prev => ({ ...prev, [field]: value }))
        break
      case "email":
        setEmailSettings(prev => ({ ...prev, [field]: value }))
        break
      case "analytics":
        setAnalyticsSettings(prev => ({ ...prev, [field]: value }))
        break
      case "security":
        setSecuritySettings(prev => ({ ...prev, [field]: value }))
        break
      case "mobile":
        setMobileSettings(prev => ({ ...prev, [field]: value }))
        break
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <Link href="/admin/dashboard" className="inline-flex items-center text-sm text-muted-foreground hover:text-primary mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Link>
        
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Settings</h1>
        </div>

        <Tabs defaultValue="site" className="space-y-4">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="site">Site</TabsTrigger>
            <TabsTrigger value="social">Social</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>
          
          {/* Site Settings */}
          <TabsContent value="site">
            <Card>
              <CardHeader>
                <CardTitle>Site Settings</CardTitle>
                <CardDescription>Manage your website's general settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="site-title">Site Title</Label>
                  <Input 
                    id="site-title" 
                    value={siteTitle} 
                    onChange={(e) => setSiteTitle(e.target.value)} 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="site-description">Site Description</Label>
                  <Textarea 
                    id="site-description" 
                    value={siteDescription} 
                    onChange={(e) => setSiteDescription(e.target.value)} 
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="footer-text">Footer Text</Label>
                  <Input 
                    id="footer-text" 
                    value={footerText} 
                    onChange={(e) => setFooterText(e.target.value)} 
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-blog">Enable Blog</Label>
                    <Switch id="enable-blog" defaultChecked />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-projects">Enable Projects</Label>
                    <Switch id="enable-projects" defaultChecked />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-contact">Enable Contact Form</Label>
                    <Switch id="enable-contact" defaultChecked />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-experience">Enable Experience Section</Label>
                    <Switch 
                      id="enable-experience" 
                      checked={showExperienceSection}
                      onCheckedChange={setShowExperienceSection}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Show/hide the section with clients served, years of experience, testimonials and companies worked with
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={() => saveSettings('Site')}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Social Media Settings */}
          <TabsContent value="social">
            <Card>
              <CardHeader>
                <CardTitle>Social Media Links</CardTitle>
                <CardDescription>Configure your social media profiles</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="github-url">GitHub URL</Label>
                  <Input 
                    id="github-url" 
                    value={socialLinks.github}
                    onChange={(e) => handleInputChange('social', 'github', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="twitter-url">Twitter URL</Label>
                  <Input 
                    id="twitter-url" 
                    value={socialLinks.twitter}
                    onChange={(e) => handleInputChange('social', 'twitter', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="linkedin-url">LinkedIn URL</Label>
                  <Input 
                    id="linkedin-url" 
                    value={socialLinks.linkedin}
                    onChange={(e) => handleInputChange('social', 'linkedin', e.target.value)}
                  />
                </div>
                
                <div className="pt-2">
                  <Button variant="outline" className="text-xs">
                    <Globe className="mr-2 h-3 w-3" />
                    Add another social profile
                  </Button>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={() => saveSettings('Social media')}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Email Settings */}
          <TabsContent value="email">
            <Card>
              <CardHeader>
                <CardTitle>Email Notifications</CardTitle>
                <CardDescription>Configure email settings and notifications</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="notify-contact">
                      Notify on contact form submission
                    </Label>
                    <Switch 
                      id="notify-contact" 
                      checked={emailSettings.notifyOnContact}
                      onCheckedChange={(checked) => handleInputChange('email', 'notifyOnContact', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="notify-subscription">
                      Notify on newsletter subscription
                    </Label>
                    <Switch 
                      id="notify-subscription" 
                      checked={emailSettings.notifyOnSubscription}
                      onCheckedChange={(checked) => handleInputChange('email', 'notifyOnSubscription', checked)}
                    />
                  </div>
                </div>
                
                <Separator className="my-4" />
                
                <div className="space-y-2">
                  <Label htmlFor="admin-email">Admin Email</Label>
                  <Input 
                    id="admin-email" 
                    type="email"
                    value={emailSettings.adminEmail}
                    onChange={(e) => handleInputChange('email', 'adminEmail', e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    All notifications will be sent to this email address
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="reply-to">Reply-To Email</Label>
                  <Input 
                    id="reply-to" 
                    type="email"
                    value={emailSettings.replyToEmail}
                    onChange={(e) => handleInputChange('email', 'replyToEmail', e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" type="button">
                  <Mail className="mr-2 h-4 w-4" />
                  Test Email
                </Button>
                <Button onClick={() => saveSettings('Email')}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Analytics Settings */}
          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Analytics Settings</CardTitle>
                <CardDescription>Configure website analytics and tracking</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-tracking">
                      Enable Analytics Tracking
                    </Label>
                    <Switch 
                      id="enable-tracking" 
                      checked={analyticsSettings.enableTracking}
                      onCheckedChange={(checked) => handleInputChange('analytics', 'enableTracking', checked)}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Collect anonymous data about how visitors use your website
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="anonymize-ip">
                      Anonymize IP Addresses
                    </Label>
                    <Switch 
                      id="anonymize-ip" 
                      checked={analyticsSettings.anonymizeIp}
                      onCheckedChange={(checked) => handleInputChange('analytics', 'anonymizeIp', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="track-outbound">
                      Track Outbound Links
                    </Label>
                    <Switch 
                      id="track-outbound" 
                      checked={analyticsSettings.trackOutboundLinks}
                      onCheckedChange={(checked) => handleInputChange('analytics', 'trackOutboundLinks', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="ga-id">Google Analytics ID</Label>
                  <Input 
                    id="ga-id" 
                    value={analyticsSettings.googleAnalyticsId}
                    onChange={(e) => handleInputChange('analytics', 'googleAnalyticsId', e.target.value)}
                    placeholder="G-XXXXXXXXXX"
                  />
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="exclude-admin">
                      Exclude Admin from Analytics
                    </Label>
                    <Switch 
                      id="exclude-admin" 
                      checked={analyticsSettings.excludeAdminFromAnalytics}
                      onCheckedChange={(checked) => handleInputChange('analytics', 'excludeAdminFromAnalytics', checked)}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={() => saveSettings('Analytics')}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Security Settings */}
          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>Configure security options for your admin area</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="two-factor">
                      Enable Two-Factor Authentication
                    </Label>
                    <Switch 
                      id="two-factor" 
                      checked={securitySettings.enableTwoFactor}
                      onCheckedChange={(checked) => handleInputChange('security', 'enableTwoFactor', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (days)</Label>
                  <Input 
                    id="session-timeout" 
                    type="number"
                    value={securitySettings.sessionTimeout}
                    onChange={(e) => handleInputChange('security', 'sessionTimeout', e.target.value)}
                    min="1"
                    max="90"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="allowed-ips">Allowed IP Addresses (one per line)</Label>
                  <Textarea 
                    id="allowed-ips" 
                    value={securitySettings.allowedIpAddresses}
                    onChange={(e) => handleInputChange('security', 'allowedIpAddresses', e.target.value)}
                    placeholder="Leave empty to allow all IPs"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Restrict admin access to these IP addresses only
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="brute-force">
                      Brute Force Protection
                    </Label>
                    <Switch 
                      id="brute-force" 
                      checked={securitySettings.enableBruteForceProtection}
                      onCheckedChange={(checked) => handleInputChange('security', 'enableBruteForceProtection', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="max-attempts">Max Login Attempts</Label>
                  <Input 
                    id="max-attempts" 
                    type="number"
                    value={securitySettings.maxLoginAttempts}
                    onChange={(e) => handleInputChange('security', 'maxLoginAttempts', e.target.value)}
                    min="1"
                    max="10"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="allowed-emails">Allowed Admin Emails (comma separated)</Label>
                  <Textarea 
                    id="allowed-emails" 
                    value={securitySettings.allowedEmails}
                    onChange={(e) => handleInputChange('security', 'allowedEmails', e.target.value)}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Only these email addresses will be able to sign in with Google
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="destructive" type="button">
                  <Shield className="mr-2 h-4 w-4" />
                  Reset Admin Password
                </Button>
                <Button onClick={() => saveSettings('Security')}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Mobile Settings */}
          <TabsContent value="mobile">
            <Card>
              <CardHeader>
                <CardTitle>Mobile Experience</CardTitle>
                <CardDescription>Configure settings for mobile devices</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-pwa">
                      Enable Progressive Web App (PWA)
                    </Label>
                    <Switch 
                      id="enable-pwa" 
                      checked={mobileSettings.enablePwa}
                      onCheckedChange={(checked) => handleInputChange('mobile', 'enablePwa', checked)}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Allow visitors to install your website as an app on their devices
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-notifications">
                      Enable Push Notifications
                    </Label>
                    <Switch 
                      id="enable-notifications" 
                      checked={mobileSettings.enableNotifications}
                      onCheckedChange={(checked) => handleInputChange('mobile', 'enableNotifications', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="install-prompt">
                      Show Install Prompt
                    </Label>
                    <Switch 
                      id="install-prompt" 
                      checked={mobileSettings.allowInstallPrompt}
                      onCheckedChange={(checked) => handleInputChange('mobile', 'allowInstallPrompt', checked)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="cache-strategy">Cache Strategy</Label>
                  <select 
                    id="cache-strategy"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={mobileSettings.cacheStrategy}
                    onChange={(e) => handleInputChange('mobile', 'cacheStrategy', e.target.value)}
                  >
                    <option value="network-first">Network First</option>
                    <option value="cache-first">Cache First</option>
                    <option value="stale-while-revalidate">Stale While Revalidate</option>
                    <option value="network-only">Network Only</option>
                  </select>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" type="button">
                  <Smartphone className="mr-2 h-4 w-4" />
                  Test Mobile View
                </Button>
                <Button onClick={() => saveSettings('Mobile')}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}