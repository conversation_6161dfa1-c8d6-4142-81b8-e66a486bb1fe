"use client"

import { redirect } from "next/navigation"
import { useSession } from "next-auth/react"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Short-circuit if we're on the login page to avoid redirect loops
    if (pathname === "/admin/login") {
      setIsLoading(false)
      return
    }

    // Check authentication status
    if (status === "loading") {
      return // Still loading, don't do anything yet
    }

    // If user is not authenticated or not admin, redirect to login
    if (status === "unauthenticated" || (status === "authenticated" && !(session as any)?.isAdmin)) {
      // Store the current path to redirect back after login
      sessionStorage.setItem("adminRedirectPath", pathname)
      redirect("/admin/login")
      return
    }

    // If we get here, user is authenticated and is admin
    setIsLoading(false)
  }, [status, session, pathname])

  if (isLoading && pathname !== "/admin/login") {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  return children
}