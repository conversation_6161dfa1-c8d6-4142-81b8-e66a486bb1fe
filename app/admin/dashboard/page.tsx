"use client"

import { useSession } from "next-auth/react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useEffect, useState } from "react"
import { PlusCircle, Edit, Trash2, ArrowLeft, Users, Eye, MapPin, Mail, BarChart3, Calendar, ArrowUpRight, Inbox, Clock, MousePointer, Cog } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

// Define types
interface Post {
  id: number;
  title: string;
  published: boolean;
  date: string;
  views: number;
}

interface PageVisit {
  path: string;
  title: string;
  views: number;
  uniqueVisitors: number;
}

interface Contact {
  id: number;
  name: string;
  email: string;
  message: string;
  date: string;
  read: boolean;
}

interface Subscriber {
  id: number;
  email: string;
  date: string;
  source: string;
}

interface GeoData {
  country: string;
  count: number;
  percentage: number;
}

interface VisitorData {
  date: string;
  visitors: number;
  uniqueVisitors: number;
}

// Component for chart visualization
const Chart = ({ data, title }: { data: any[]; title: string }) => {
  // Calculate the maximum value for scaling
  const maxValue = Math.max(...data.map(item => Math.max(item.visitors || 0, item.uniqueVisitors || 0)));
  
  return (
    <div className="w-full mt-2">
      <div className="text-sm font-medium mb-4">{title}</div>
      <div className="flex items-end h-40 gap-1">
        {data.map((item, index) => (
          <div key={index} className="flex-1 flex flex-col items-center group">
            <div className="relative w-full flex flex-col items-center justify-end h-[calc(100%-24px)]">
              {item.visitors !== undefined && (
                <div 
                  className="w-full bg-primary/70 rounded-t-sm group-hover:bg-primary transition-colors"
                  style={{ height: `${(item.visitors / maxValue) * 100}%` }}
                >
                  <div className="opacity-0 group-hover:opacity-100 absolute bottom-full mb-1 left-1/2 transform -translate-x-1/2 bg-background border border-border px-2 py-1 rounded text-xs whitespace-nowrap">
                    {item.visitors} visitors
                  </div>
                </div>
              )}
              {item.uniqueVisitors !== undefined && (
                <div 
                  className="w-full bg-primary/30 border-t border-background rounded-t-sm mt-auto group-hover:bg-primary/50 transition-colors absolute bottom-0"
                  style={{ height: `${(item.uniqueVisitors / maxValue) * 100}%` }}
                >
                  <div className="opacity-0 group-hover:opacity-100 absolute bottom-full mb-1 left-1/2 transform -translate-x-1/2 bg-background border border-border px-2 py-1 rounded text-xs whitespace-nowrap">
                    {item.uniqueVisitors} unique
                  </div>
                </div>
              )}
            </div>
            <div className="text-xs mt-1 text-muted-foreground">
              {item.date.split(' ')[0]}
            </div>
          </div>
        ))}
      </div>
      <div className="flex justify-between mt-2">
        <div className="flex items-center gap-2 text-xs">
          <div className="w-3 h-3 bg-primary/70 rounded-sm"></div>
          <span>Total Visitors</span>
        </div>
        <div className="flex items-center gap-2 text-xs">
          <div className="w-3 h-3 bg-primary/30 rounded-sm"></div>
          <span>Unique Visitors</span>
        </div>
      </div>
    </div>
  );
};

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [posts, setPosts] = useState<Post[]>([])
  const [pageVisits, setPageVisits] = useState<PageVisit[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [subscribers, setSubscribers] = useState<Subscriber[]>([])
  const [geoData, setGeoData] = useState<GeoData[]>([])
  const [visitorData, setVisitorData] = useState<VisitorData[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  // Only fetch data when authenticated
  useEffect(() => {
    if (status === "authenticated" && (session as any)?.isAdmin) {
      // Fetch all data
      fetchAllData()
    }
  }, [status, session])

  const fetchAllData = async () => {
    // In a real app, these would be API calls
    // Using mock data for example
    
    // Fetch blog posts
    const postsData = [
      { id: 1, title: "React Hooks Explained", published: true, date: "June 15, 2023", views: 1245 },
      { id: 2, title: "Building a REST API with Node.js", published: true, date: "April 22, 2023", views: 892 },
      { id: 3, title: "Understanding TypeScript Generics", published: true, date: "June 5, 2023", views: 567 },
      { id: 4, title: "CSS Grid Layout: A Complete Guide", published: true, date: "May 30, 2023", views: 721 },
      { id: 5, title: "Web Performance Optimization Tips", published: false, date: "June 12, 2023", views: 113 }
    ]
    setPosts(postsData)
    
    // Fetch page visits
    const pagesData = [
      { path: "/", title: "Home Page", views: 3420, uniqueVisitors: 2105 },
      { path: "/blog", title: "Blog Index", views: 1856, uniqueVisitors: 1214 },
      { path: "/blog/react-hooks-explained", title: "React Hooks Explained", views: 1245, uniqueVisitors: 983 },
      { path: "/projects", title: "Projects Index", views: 1680, uniqueVisitors: 1120 },
      { path: "/contact", title: "Contact Page", views: 978, uniqueVisitors: 845 },
      { path: "/projects/ecommerce-platform", title: "E-commerce Project", views: 723, uniqueVisitors: 602 }
    ]
    setPageVisits(pagesData)
    
    // Fetch contact submissions
    const contactsData = [
      { id: 1, name: "John Smith", email: "<EMAIL>", message: "I'd like to discuss a potential project...", date: "June 18, 2023", read: true },
      { id: 2, name: "Sarah Johnson", email: "<EMAIL>", message: "Your portfolio is impressive! I'm looking for a developer to...", date: "June 15, 2023", read: true },
      { id: 3, name: "Michael Brown", email: "<EMAIL>", message: "Hello, I'm interested in your services for a new e-commerce website...", date: "June 12, 2023", read: false },
      { id: 4, name: "Emma Wilson", email: "<EMAIL>", message: "Do you offer consulting services? I'd like to schedule a call to discuss...", date: "June 10, 2023", read: false },
      { id: 5, name: "Daniel Lee", email: "<EMAIL>", message: "I have a question about your project X. Could you provide more details on...", date: "June 5, 2023", read: true }
    ]
    setContacts(contactsData)
    
    // Fetch newsletter subscribers
    const subscribersData = [
      { id: 1, email: "<EMAIL>", date: "June 10, 2023", source: "Blog post" },
      { id: 2, email: "<EMAIL>", date: "June 8, 2023", source: "Home page" },
      { id: 3, email: "<EMAIL>", date: "June 5, 2023", source: "Projects page" },
      { id: 4, email: "<EMAIL>", date: "June 3, 2023", source: "Blog post" },
      { id: 5, email: "<EMAIL>", date: "June 1, 2023", source: "Home page" },
      { id: 6, email: "<EMAIL>", date: "May 30, 2023", source: "Contact page" },
      { id: 7, email: "<EMAIL>", date: "May 28, 2023", source: "Blog post" },
      { id: 8, email: "<EMAIL>", date: "May 25, 2023", source: "Projects page" }
    ]
    setSubscribers(subscribersData)
    
    // Fetch geographic data
    const geoData = [
      { country: "United States", count: 2450, percentage: 42.3 },
      { country: "United Kingdom", count: 876, percentage: 15.1 },
      { country: "Canada", count: 654, percentage: 11.3 },
      { country: "Germany", count: 432, percentage: 7.5 },
      { country: "India", count: 321, percentage: 5.5 },
      { country: "Australia", count: 258, percentage: 4.5 },
      { country: "France", count: 201, percentage: 3.5 },
      { country: "Others", count: 598, percentage: 10.3 }
    ]
    setGeoData(geoData)
    
    // Fetch visitor data (last 7 days)
    const visitorData = [
      { date: "Jun 18", visitors: 142, uniqueVisitors: 95 },
      { date: "Jun 17", visitors: 156, uniqueVisitors: 110 },
      { date: "Jun 16", visitors: 132, uniqueVisitors: 88 },
      { date: "Jun 15", visitors: 187, uniqueVisitors: 134 },
      { date: "Jun 14", visitors: 165, uniqueVisitors: 120 },
      { date: "Jun 13", visitors: 145, uniqueVisitors: 98 },
      { date: "Jun 12", visitors: 128, uniqueVisitors: 85 }
    ]
    setVisitorData(visitorData)
    
    setLoading(false)
  }

  const totalPageViews = pageVisits.reduce((sum, page) => sum + page.views, 0)
  const totalUniqueVisitors = pageVisits.reduce((sum, page) => sum + page.uniqueVisitors, 0)
  const averageTimeOnSite = "2m 34s" // Mock data
  const bounceRate = "38.7%" // Mock data
  
  if (loading) {
    return <div className="p-8 text-center">Loading dashboard...</div>
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <Link href="/" className="inline-flex items-center text-sm text-muted-foreground hover:text-primary mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to site
        </Link>
        
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <div className="flex gap-2">
            <Button asChild variant="outline">
              <Link href="/admin/settings">
                <Cog className="mr-2 h-4 w-4" />
                Settings
              </Link>
            </Button>
            <Button asChild>
              <Link href="/admin/posts/new">
                <PlusCircle className="mr-2 h-4 w-4" />
                New Post
              </Link>
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="traffic">Traffic</TabsTrigger>
            <TabsTrigger value="contacts">Messages</TabsTrigger>
            <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          </TabsList>
          
          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("traffic")}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium group-hover:text-primary transition-colors">Total Visitors</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalPageViews.toLocaleString()}</div>
                  <div className="flex justify-between">
                    <p className="text-xs text-muted-foreground">+14.2% from last month</p>
                    <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View details →</span>
                  </div>
                </CardContent>
              </Card>
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("traffic")}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium group-hover:text-primary transition-colors">Unique Visitors</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalUniqueVisitors.toLocaleString()}</div>
                  <div className="flex justify-between">
                    <p className="text-xs text-muted-foreground">+5.3% from last month</p>
                    <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View details →</span>
                  </div>
                </CardContent>
              </Card>
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("contacts")}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium group-hover:text-primary transition-colors">Contact Messages</CardTitle>
                  <Inbox className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{contacts.length}</div>
                  <div className="flex justify-between">
                    <p className="text-xs text-muted-foreground">{contacts.filter(c => !c.read).length} unread messages</p>
                    <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View messages →</span>
                  </div>
                </CardContent>
              </Card>
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("subscribers")}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium group-hover:text-primary transition-colors">Newsletter Subscribers</CardTitle>
                  <Mail className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{subscribers.length}</div>
                  <div className="flex justify-between">
                    <p className="text-xs text-muted-foreground">+2 new this week</p>
                    <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity">View subscribers →</span>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4 hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("traffic")}>
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">Visitor Traffic</CardTitle>
                  <CardDescription>Daily visitor traffic over the last 7 days</CardDescription>
                </CardHeader>
                <CardContent>
                  <Chart data={visitorData} title="Visitor statistics" />
                </CardContent>
              </Card>
              <Card className="col-span-3 hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("traffic")}>
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">Top Pages</CardTitle>
                  <CardDescription>Most visited pages on your site</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {pageVisits.slice(0, 4).map((page, i) => (
                      <div key={i} className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-2 ${i === 0 ? 'bg-primary' : 'bg-muted'}`}></div>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none truncate">{page.title}</p>
                          <p className="text-xs text-muted-foreground">{page.path}</p>
                        </div>
                        <div className="text-sm font-medium">{page.views.toLocaleString()}</div>
                      </div>
                    ))}
                  </div>
                  <div className="w-full mt-4 text-xs flex items-center justify-center text-primary">
                    View all
                    <ArrowUpRight className="h-3 w-3 ml-1" />
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("content")}>
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">Recent Posts</CardTitle>
                  <CardDescription>Latest blog posts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {posts.slice(0, 3).map((post, i) => (
                      <div key={i} className="flex items-start">
                        <div className="mr-2 mt-0.5 rounded-full bg-muted p-1">
                          <Calendar className="h-3 w-3" />
                        </div>
                        <div className="space-y-1">
                          <p className="text-sm font-medium">{post.title}</p>
                          <div className="flex items-center text-xs text-muted-foreground">
                            <span>{post.date}</span>
                            <span className="mx-1">•</span>
                            <span>{post.views} views</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="w-full mt-4 text-xs flex items-center justify-center text-primary">
                    Manage posts
                    <ArrowUpRight className="h-3 w-3 ml-1" />
                  </div>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("traffic")}>
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">Geographic Data</CardTitle>
                  <CardDescription>Top visitor locations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {geoData.slice(0, 5).map((item, i) => (
                      <div key={i} className="flex items-center">
                        <div className="mr-2">
                          <MapPin className="h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex justify-between items-center">
                            <p className="text-sm font-medium leading-none">{item.country}</p>
                            <span className="text-xs text-muted-foreground">{item.percentage}%</span>
                          </div>
                          <div className="w-full h-1.5 bg-muted rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary rounded-full" 
                              style={{ width: `${item.percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="w-full mt-4 text-xs flex items-center justify-center text-primary">
                    View all locations
                    <ArrowUpRight className="h-3 w-3 ml-1" />
                  </div>
                </CardContent>
              </Card>
              
              <Card className="hover:shadow-md transition-shadow cursor-pointer group" onClick={() => setActiveTab("traffic")}>
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">Key Metrics</CardTitle>
                  <CardDescription>Website performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <div className="mr-2 p-1 rounded-full bg-muted">
                        <Clock className="h-3 w-3" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Avg. Time on Site</p>
                        <div className="text-xs text-muted-foreground">
                          How long visitors stay on your site
                        </div>
                      </div>
                      <div className="text-sm font-medium">{averageTimeOnSite}</div>
                    </div>
                    
                    <div className="flex items-center">
                      <div className="mr-2 p-1 rounded-full bg-muted">
                        <MousePointer className="h-3 w-3" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Bounce Rate</p>
                        <div className="text-xs text-muted-foreground">
                          Percentage of single-page visits
                        </div>
                      </div>
                      <div className="text-sm font-medium">{bounceRate}</div>
                    </div>
                    
                    <div className="flex items-center">
                      <div className="mr-2 p-1 rounded-full bg-muted">
                        <BarChart3 className="h-3 w-3" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Conversion Rate</p>
                        <div className="text-xs text-muted-foreground">
                          Contact form submissions
                        </div>
                      </div>
                      <div className="text-sm font-medium">2.8%</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* Content Tab */}
          <TabsContent value="content">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Blog Posts</CardTitle>
                    <CardDescription>Manage your blog content</CardDescription>
                  </div>
                  <Button asChild>
                    <Link href="/admin/posts/new">
                      <PlusCircle className="mr-2 h-4 w-4" />
                      New Post
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead>
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <th className="h-12 px-4 text-left align-middle font-medium">Title</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Views</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Date</th>
                          <th className="h-12 px-4 text-right align-middle font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {posts.map((post) => (
                          <tr key={post.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                            <td className="p-4 align-middle">{post.title}</td>
                            <td className="p-4 align-middle">
                              <Badge variant={post.published ? "default" : "secondary"}>
                                {post.published ? 'Published' : 'Draft'}
                              </Badge>
                            </td>
                            <td className="p-4 align-middle">{post.views.toLocaleString()}</td>
                            <td className="p-4 align-middle">{post.date}</td>
                            <td className="p-4 align-middle text-right">
                              <div className="flex justify-end space-x-2">
                                <Button variant="outline" size="sm" asChild>
                                  <Link href={`/admin/posts/edit/${post.id}`}>
                                    <Edit className="h-4 w-4" />
                                  </Link>
                                </Button>
                                <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Traffic Tab */}
          <TabsContent value="traffic" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Page Views</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalPageViews.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">Last 30 days</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Unique Visitors</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalUniqueVisitors.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">Last 30 days</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Time on Site</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{averageTimeOnSite}</div>
                  <p className="text-xs text-muted-foreground">+8.1% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{bounceRate}</div>
                  <p className="text-xs text-muted-foreground">-2.3% from last month</p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Traffic Overview</CardTitle>
                <CardDescription>Daily visitor data for the last 7 days</CardDescription>
              </CardHeader>
              <CardContent>
                <Chart data={visitorData} title="Visitor statistics" />
              </CardContent>
            </Card>
            
            <div className="grid gap-4 md:grid-cols-5">
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Page Analysis</CardTitle>
                  <CardDescription>Performance metrics for each page</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <div className="relative w-full overflow-auto">
                      <table className="w-full caption-bottom text-sm">
                        <thead>
                          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                            <th className="h-12 px-4 text-left align-middle font-medium">Page</th>
                            <th className="h-12 px-4 text-left align-middle font-medium">Views</th>
                            <th className="h-12 px-4 text-left align-middle font-medium">Unique</th>
                            <th className="h-12 px-4 text-left align-middle font-medium">Avg. Time</th>
                          </tr>
                        </thead>
                        <tbody>
                          {pageVisits.map((page, i) => (
                            <tr key={i} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                              <td className="p-4 align-middle">
                                <div className="font-medium">{page.title}</div>
                                <div className="text-xs text-muted-foreground">{page.path}</div>
                              </td>
                              <td className="p-4 align-middle">{page.views.toLocaleString()}</td>
                              <td className="p-4 align-middle">{page.uniqueVisitors.toLocaleString()}</td>
                              <td className="p-4 align-middle">{Math.floor(Math.random() * 3) + 1}m {Math.floor(Math.random() * 50) + 10}s</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle>Geographic Distribution</CardTitle>
                  <CardDescription>Visitor locations breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {geoData.map((item, i) => (
                      <div key={i} className="flex items-center">
                        <div className="mr-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex justify-between items-center">
                            <p className="text-sm font-medium leading-none">{item.country}</p>
                            <span className="text-sm">{item.count.toLocaleString()}</span>
                          </div>
                          <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary rounded-full" 
                              style={{ width: `${item.percentage}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-muted-foreground text-right">{item.percentage}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* Contacts Tab */}
          <TabsContent value="contacts">
            <Card>
              <CardHeader>
                <CardTitle>Contact Messages</CardTitle>
                <CardDescription>Messages from the contact form</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead>
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <th className="h-12 px-4 text-left align-middle font-medium">From</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Message</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Date</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                          <th className="h-12 px-4 text-right align-middle font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {contacts.map((contact) => (
                          <tr key={contact.id} className={`border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted ${!contact.read ? 'bg-muted/20' : ''}`}>
                            <td className="p-4 align-middle">
                              <div className="font-medium">{contact.name}</div>
                              <div className="text-xs text-muted-foreground">{contact.email}</div>
                            </td>
                            <td className="p-4 align-middle">
                              <div className="truncate max-w-[300px]">{contact.message}</div>
                            </td>
                            <td className="p-4 align-middle whitespace-nowrap">{contact.date}</td>
                            <td className="p-4 align-middle">
                              <Badge variant={contact.read ? "outline" : "default"}>
                                {contact.read ? 'Read' : 'Unread'}
                              </Badge>
                            </td>
                            <td className="p-4 align-middle text-right">
                              <Button asChild variant="ghost" size="sm">
                                <Link href={`/admin/messages/${contact.id}`}>View</Link>
                              </Button>
                              <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                                Delete
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Subscribers Tab */}
          <TabsContent value="subscribers">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Newsletter Subscribers</CardTitle>
                    <CardDescription>People subscribed to your newsletter</CardDescription>
                  </div>
                  <Button variant="outline">
                    Export CSV
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead>
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <th className="h-12 px-4 text-left align-middle font-medium">Email</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Date</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">Source</th>
                          <th className="h-12 px-4 text-right align-middle font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {subscribers.map((subscriber) => (
                          <tr key={subscriber.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                            <td className="p-4 align-middle font-medium">{subscriber.email}</td>
                            <td className="p-4 align-middle">{subscriber.date}</td>
                            <td className="p-4 align-middle">{subscriber.source}</td>
                            <td className="p-4 align-middle text-right">
                              <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                                Remove
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-muted-foreground">Showing 8 of 8 subscribers</div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" disabled>
                      Previous
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      Next
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-4">
                <div className="flex items-center justify-between w-full">
                  <div>
                    <div className="text-sm font-medium">Subscriber Growth</div>
                    <div className="text-xs text-muted-foreground">+16.4% from last month</div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-primary mr-1"></div>
                      <span className="text-xs">New: 3</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-muted mr-1"></div>
                      <span className="text-xs">Unsubscribed: 0</span>
                    </div>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}