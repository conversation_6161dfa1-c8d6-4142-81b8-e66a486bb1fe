"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Mail, Clock, User, Calendar, Flag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface Message {
  id: number
  name: string
  email: string
  message: string
  date: string
  read: boolean
}

export default function MessageView() {
  const params = useParams()
  const router = useRouter()
  const messageId = params.id as string
  const [message, setMessage] = useState<Message | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // In a real app, this would be an API call
    // Mock data for example
    setTimeout(() => {
      const messageData = {
        id: parseInt(messageId),
        name: "<PERSON>",
        email: "<EMAIL>",
        message: "I'd like to discuss a potential project for my company. We are looking for someone with your skillset to help us develop a blockchain solution for our supply chain management system. The project would involve creating smart contracts and developing a user-friendly interface for our team to interact with the blockchain. We're particularly impressed with your portfolio and would love to set up a call to discuss this further. Please let me know your availability in the coming week. Looking forward to potentially working together.",
        date: "June 18, 2023",
        read: false
      }
      
      setMessage(messageData)
      setLoading(false)
      
      // Mark as read in a real app, this would update the database
    }, 500)
  }, [messageId])

  const handleDelete = () => {
    // In a real app, this would be an API call to delete the message
    router.push("/admin/dashboard")
  }

  const handleReply = () => {
    // In a real app, this would open an email client or a form to reply
    window.open(`mailto:${message?.email}`)
  }

  if (loading) {
    return (
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="p-8 text-center">Loading message...</div>
      </div>
    )
  }

  if (!message) {
    return (
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="p-8 text-center">Message not found</div>
      </div>
    )
  }

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <Link href="/admin/dashboard" className="inline-flex items-center text-sm text-muted-foreground hover:text-primary mb-6">
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Dashboard
      </Link>
      
      <Card className="shadow-md">
        <CardHeader className="border-b pb-4">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{message.name}</CardTitle>
              <CardDescription className="flex items-center mt-1">
                <Mail className="h-4 w-4 mr-1" />
                <a href={`mailto:${message.email}`} className="hover:underline">{message.email}</a>
              </CardDescription>
            </div>
            <Badge variant={message.read ? "outline" : "default"}>
              {message.read ? 'Read' : 'Unread'}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="pt-6 pb-6">
          <div className="flex items-center text-sm text-muted-foreground mb-6 space-x-4">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              <span>{message.date}</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              <span>{new Date(message.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              <span>Contact Form</span>
            </div>
          </div>
          
          <div className="bg-muted/30 p-6 rounded-lg border">
            <p className="whitespace-pre-line">{message.message}</p>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between border-t pt-4">
          <Button variant="outline" className="text-destructive hover:bg-destructive/10" onClick={handleDelete}>
            <Flag className="mr-2 h-4 w-4" />
            Mark as Spam
          </Button>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => router.push('/admin/dashboard')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button onClick={handleReply}>
              <Mail className="mr-2 h-4 w-4" />
              Reply
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}