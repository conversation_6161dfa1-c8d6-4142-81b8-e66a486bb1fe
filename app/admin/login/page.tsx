"use client"

import { useState, useEffect } from "react"
import { signIn, useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Mail } from "lucide-react"
import { Separator } from "@/components/ui/separator"

export default function AdminLogin() {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)
  const [redirectPath, setRedirectPath] = useState("/admin/dashboard")
  const router = useRouter()
  const { data: session, status } = useSession()
  
  // Get any stored redirect path from sessionStorage
  useEffect(() => {
    // This runs only in the browser
    const storedPath = sessionStorage.getItem("adminRedirectPath")
    if (storedPath) {
      setRedirectPath(storedPath)
    }
  }, [])
  
  // Redirect if already authenticated and is admin
  useEffect(() => {
    if (status === "authenticated" && (session as any)?.isAdmin === true) {
      // Clear the stored path after successful redirect
      sessionStorage.removeItem("adminRedirectPath")
      router.push(redirectPath)
    }
  }, [status, session, router, redirectPath])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    
    try {
      const result = await signIn("credentials", {
        username,
        password,
        redirect: false,
      })
      
      if (result?.error) {
        setError("Invalid username or password")
        setLoading(false)
      } else {
        // Successfully logged in
        // The useEffect above will handle redirect
      }
    } catch (error) {
      setError("An error occurred during login")
      setLoading(false)
    }
  }

  if (status === "loading" || (status === "authenticated" && (session as any)?.isAdmin === true)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-4">
            <Shield className="h-8 w-8 text-primary" />
          </div>
          <h2 className="mt-6 text-3xl font-bold">Admin Login</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Enter your credentials to access the dashboard
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="username" className="block text-sm font-medium">
                Username
              </label>
              <Input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="mt-1"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium">
                Password
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={loading}
          >
            {loading ? "Logging in..." : "Sign in with Credentials"}
          </Button>
          
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center">
              <span className="bg-background px-2 text-muted-foreground text-sm">
                OR
              </span>
            </div>
          </div>
          
          <Button
            type="button"
            className="w-full bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700"
            onClick={() => signIn('google', { callbackUrl: redirectPath })}
            disabled={loading}
          >
            <Mail className="mr-2 h-4 w-4" />
            Sign in with Google
          </Button>
        </form>
      </div>
    </div>
  )
}
