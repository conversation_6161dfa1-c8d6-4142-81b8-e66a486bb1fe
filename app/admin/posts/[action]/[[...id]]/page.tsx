"use client"

import { useState, useEffect, useRef } from "react"
import { useSession } from "next-auth/react"
import { useRouter, useParams } from "next/navigation"
import { ArrowLeft, Save, Image, Eye, Edit, FileImage, Plus } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ToastAction } from "@/components/ui/toast"
import { useToast } from "@/components/ui/use-toast"

// Simple markdown-like parser for the preview
function parseContent(content: string) {
  // Replace patterns with HTML elements
  let html = content
    // Bold text: **text** or __text__
    .replace(/(\*\*|__)(.*?)\1/g, '<strong>$2</strong>')
    // Italic text: *text* or _text_
    .replace(/(\*|_)(.*?)\1/g, '<em>$2</em>')
    // Underlined text: ++text++
    .replace(/\+\+(.*?)\+\+/g, '<u>$1</u>')
    // Headers: # Header, ## Header, ### Header
    .replace(/^# (.*$)/gm, '<h1 class="text-4xl font-bold my-4">$1</h1>')
    .replace(/^## (.*$)/gm, '<h2 class="text-3xl font-bold my-3">$1</h2>')
    .replace(/^### (.*$)/gm, '<h3 class="text-2xl font-bold my-2">$1</h3>')
    // Links: [text](url)
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:underline">$1</a>')
    // Lists: - item
    .replace(/^- (.*$)/gm, '<li class="ml-6 list-disc">$1</li>')
    .replace(/(<li.*?>\s*<\/li>\s*)+/g, '<ul class="my-4">$&</ul>')
    // Blockquotes: > text
    .replace(/^> (.*$)/gm, '<blockquote class="pl-4 border-l-4 border-gray-300 italic my-4">$1</blockquote>')
    // Code blocks: ```code```
    .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-4 rounded my-4 overflow-x-auto"><code>$1</code></pre>')
    // Inline code: `code`
    .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
    // Images: ![alt](url)
    .replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1" class="my-4 max-w-full h-auto rounded-lg shadow-md" />')
    // Line breaks
    .replace(/\n\s*\n/g, '</p><p class="my-4">')
    
  // Wrap in paragraph tags if not already wrapped
  if (!html.startsWith('<')) {
    html = `<p class="my-4">${html}</p>`;
  }
  
  return html;
}

// Preview component to render the blog post
function BlogPreview({ post }: { post: { title: string; coverImage: string | null; tags: string[]; content: string; published: boolean } }) {
  return (
    <div className="blog-preview">
      <h1 className="text-4xl font-extrabold mb-6 text-gray-900">{post.title}</h1>
      
      {post.coverImage && (
        <div className="w-full mb-8 rounded-lg overflow-hidden shadow-lg">
          <img 
            src={post.coverImage} 
            alt="Cover image" 
            className="w-full object-contain max-h-96"
          />
        </div>
      )}
      
      <div className="flex flex-wrap gap-2 mb-8">
        {post.tags.map((tag) => (
          <Badge key={tag} variant="secondary" className="text-sm py-1 px-3 bg-gray-200 text-gray-800">
            {tag}
          </Badge>
        ))}
      </div>
      
      <div className="prose prose-lg max-w-none text-gray-800">
        <div dangerouslySetInnerHTML={{ __html: parseContent(post.content) }} />
      </div>
      
      <div className="mt-8 text-sm text-gray-500 border-t pt-4">
        Status: <span className={post.published ? "text-green-600 font-medium" : "text-amber-600 font-medium"}>
          {post.published ? 'Published' : 'Draft'}
        </span>
      </div>
    </div>
  )
}

export default function BlogEditor() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const isEditMode = params.action === "edit"
  const postId = params.id ? params.id[0] : null
  const { toast } = useToast()

  const [post, setPost] = useState({
    title: "",
    content: "",
    tags: [] as string[],
    coverImage: null as string | null,
    published: false
  })
  const [newTag, setNewTag] = useState("")
  const [loading, setLoading] = useState(true)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadingInlineImage, setUploadingInlineImage] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const inlineImageInputRef = useRef<HTMLInputElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated" || (status === "authenticated" && !(session as any)?.isAdmin)) {
      router.push("/admin/login")
    } else if (status === "authenticated" && isEditMode && postId) {
      // Fetch post data if in edit mode
      fetchPost(postId)
    } else {
      setLoading(false)
    }
  }, [status, session, router, isEditMode, postId])

  const fetchPost = async (id: string) => {
    // In a real app, you'd fetch from your API
    try {
      const response = await fetch(`/api/blog/${id}`)
      if (!response.ok) throw new Error('Failed to fetch post')
      const data = await response.json()
      setPost(data)
    } catch (error) {
      console.error('Error fetching post:', error)
      // Use fallback/mock data for example
      setPost({
        title: "Example Post Title",
        content: "# Introduction\n\nThis is the **full content** of the blog post. It would be much longer in a real post.\n\n## Key Points\n\n- This is a *bullet point* with italic text\n- Here's another point with ++underlined text++\n\n### Code Example\n\n```\nconst greeting = 'Hello world!';\nconsole.log(greeting);\n```\n\n> This is a blockquote with some important information.\n\nYou can also add [links to websites](https://example.com) easily.\n\n![Example Image](/placeholder.jpg)",
        tags: ["React", "Next.js"],
        coverImage: "/placeholder.svg",
        published: true
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAddTag = () => {
    if (newTag && !post.tags.includes(newTag)) {
      setPost({ ...post, tags: [...post.tags, newTag] })
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setPost({ ...post, tags: post.tags.filter(tag => tag !== tagToRemove) })
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0]) return
    
    const file = e.target.files[0]
    setIsUploading(true)
    
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        throw new Error('Failed to upload image')
      }
      
      const data = await response.json()
      setPost({ ...post, coverImage: data.url })
      
      toast({
        title: "Cover image uploaded",
        description: "Your cover image has been uploaded successfully.",
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        variant: "destructive",
        title: "Upload failed",
        description: "There was a problem uploading your image.",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleInlineImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0] || !textareaRef.current) return
    
    const file = e.target.files[0]
    setUploadingInlineImage(true)
    
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        throw new Error('Failed to upload image')
      }
      
      const data = await response.json()
      
      // Get current cursor position
      const textarea = textareaRef.current
      const cursorPos = textarea.selectionStart
      
      // Insert markdown image tag at cursor position
      const imageAlt = file.name.split('.')[0] || 'Image'
      const imageMarkdown = `![${imageAlt}](${data.url})`
      
      const newContent = 
        post.content.substring(0, cursorPos) + 
        imageMarkdown + 
        post.content.substring(cursorPos)
      
      setPost({ ...post, content: newContent })
      
      // Set cursor position after the inserted image markdown
      setTimeout(() => {
        textarea.focus()
        textarea.selectionStart = cursorPos + imageMarkdown.length
        textarea.selectionEnd = cursorPos + imageMarkdown.length
      }, 0)
      
      toast({
        title: "Image inserted",
        description: "Your image has been uploaded and inserted into the content.",
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        variant: "destructive",
        title: "Upload failed",
        description: "There was a problem uploading your image.",
      })
    } finally {
      setUploadingInlineImage(false)
      if (inlineImageInputRef.current) {
        inlineImageInputRef.current.value = ''
      }
    }
  }

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }
  
  const triggerInlineImageInput = () => {
    if (inlineImageInputRef.current) {
      inlineImageInputRef.current.click()
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = isEditMode ? `/api/blog/${postId}` : '/api/blog'
      const method = isEditMode ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(post),
      })
      
      if (!response.ok) {
        throw new Error('Failed to save post')
      }
      
      toast({
        title: isEditMode ? "Post updated" : "Post created",
        description: `Your post has been ${isEditMode ? 'updated' : 'created'} successfully.`,
      })
      
      router.push("/admin/dashboard")
    } catch (error) {
      console.error('Save error:', error)
      toast({
        variant: "destructive",
        title: "Save failed",
        description: "There was a problem saving your post.",
      })
    }
  }

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode)
  }

  if (loading) {
    return <div className="p-8 text-center">Loading editor...</div>
  }

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <Link href="/admin/dashboard" className="inline-flex items-center text-sm text-muted-foreground hover:text-primary mb-6">
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Dashboard
      </Link>
      
      <h1 className="text-3xl font-bold mb-8">
        {isEditMode ? "Edit Post" : "Create New Post"}
      </h1>
      
      {isPreviewMode ? (
        <div className="bg-white p-8 rounded-lg border shadow-lg">
          <div className="mb-6 flex justify-end">
            <Button 
              type="button" 
              onClick={togglePreview}
              variant="outline"
            >
              <Edit className="mr-2 h-4 w-4" />
              Back to Editor
            </Button>
          </div>
          <BlogPreview post={post} />
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium mb-1">
              Post Title
            </label>
            <Input
              id="title"
              value={post.title}
              onChange={(e) => setPost({ ...post, title: e.target.value })}
              placeholder="Enter post title"
              required
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">
              Cover Image
            </label>
            <div className="mt-1 flex items-center">
              {post.coverImage ? (
                <div className="relative w-full rounded-lg overflow-hidden bg-gray-100 mb-4">
                  <img 
                    src={post.coverImage} 
                    alt="Cover preview" 
                    className="w-full object-contain max-h-64"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={() => setPost({ ...post, coverImage: null })}
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <div className="w-full">
                  <div 
                    className="flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-lg hover:border-gray-400 transition-colors cursor-pointer"
                    onClick={triggerFileInput}
                  >
                    <div className="space-y-1 text-center">
                      {isUploading ? (
                        <div className="flex flex-col items-center">
                          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mb-2"></div>
                          <p className="text-sm text-gray-500">Uploading...</p>
                        </div>
                      ) : (
                        <>
                          <Image className="mx-auto h-12 w-12 text-gray-400" />
                          <div className="flex text-sm text-gray-600">
                            <span className="relative cursor-pointer rounded-md font-medium text-blue-600 hover:text-blue-500">
                              Upload a file
                            </span>
                            <p className="pl-1">or drag and drop</p>
                          </div>
                          <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                        </>
                      )}
                    </div>
                  </div>
                  <input 
                    ref={fileInputRef}
                    id="file-upload" 
                    name="file-upload" 
                    type="file" 
                    className="hidden" 
                    onChange={handleImageUpload} 
                    accept="image/*" 
                    disabled={isUploading}
                  />
                </div>
              )}
            </div>
          </div>
          
          <div>
            <label htmlFor="content" className="block text-sm font-medium mb-1">
              Content
            </label>
            <div className="mb-2 text-xs text-gray-500">
              Supports markdown: **bold**, *italic*, ++underline++, # headings, - lists, {'>'} quotes, `code`, [links](url), ![alt](image-url)
            </div>
            <div className="relative">
              <Textarea
                ref={textareaRef}
                id="content"
                value={post.content}
                onChange={(e) => setPost({ ...post, content: e.target.value })}
                placeholder="Write your post content here..."
                rows={12}
                className="w-full font-mono pr-12"
              />
              <div className="absolute right-3 top-3 flex flex-col gap-2">
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  className="h-8 w-8 rounded-full bg-white"
                  onClick={triggerInlineImageInput}
                  disabled={uploadingInlineImage}
                  title="Insert image"
                >
                  {uploadingInlineImage ? (
                    <div className="animate-spin h-4 w-4 border-b-2 border-gray-900"></div>
                  ) : (
                    <FileImage className="h-4 w-4" />
                  )}
                </Button>
                <input
                  ref={inlineImageInputRef}
                  type="file"
                  className="hidden"
                  onChange={handleInlineImageUpload}
                  accept="image/*"
                  disabled={uploadingInlineImage}
                />
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Tip: Add images by clicking the image button or using ![alt](url) syntax
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">
              Tags
            </label>
            <div className="flex flex-wrap gap-2 mb-3">
              {post.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-sm py-1 px-2">
                  {tag}
                  <button
                    type="button"
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    ×
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                className="flex-grow"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleAddTag}
              >
                Add
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center">
              <input
                id="published"
                name="published"
                type="checkbox"
                checked={post.published}
                onChange={(e) => setPost({ ...post, published: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary-light border-gray-300 rounded"
              />
              <label htmlFor="published" className="ml-2 block text-sm">
                Publish immediately
              </label>
            </div>
            
            <div className="flex gap-3">
              <Button type="button" variant="outline" onClick={togglePreview}>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </Button>
              <Button type="submit">
                <Save className="mr-2 h-4 w-4" />
                {isEditMode ? "Update Post" : "Save Post"}
              </Button>
            </div>
          </div>
        </form>
      )}
    </div>
  )
}