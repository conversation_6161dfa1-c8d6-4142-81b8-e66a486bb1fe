import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { AuthOptions } from 'next-auth';

export const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (credentials?.username === process.env.ADMIN_USERNAME && 
            credentials?.password === process.env.ADMIN_PASSWORD) {
          return { id: '1', name: 'Admin', email: '<EMAIL>', role: 'admin' };
        }
        return null;
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    }),
  ],
  pages: {
    signIn: '/admin/login',
    error: '/admin/error',
  },
  session: {
    // Set strategy to JWT for simplified session handling
    strategy: "jwt",
    // Extend default session expiration time (default is 30 days)
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async session({ session, token }) {
      // Add admin role to session from token
      (session as any).isAdmin = (token as any).isAdmin || false;
      return session;
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        // If using credentials provider, check if this is the admin user
        if (user.email === '<EMAIL>' || user.email === process.env.ADMIN_EMAIL) {
          (token as any).isAdmin = true;
        }
      }
      // For Google login, check if email is in allowed list
      if (account?.provider === 'google' && user?.email) {
        const allowedEmails = (process.env.ALLOWED_ADMIN_EMAILS || '').split(',');
        if (allowedEmails.includes(user.email)) {
          (token as any).isAdmin = true;
        }
      }
      return token;
    },
    async signIn({ account, profile }) {
      // Allow all credential logins to go through
      if (account?.provider === 'credentials') {
        return true;
      }
      
      // For Google login, restrict to specific emails
      if (account?.provider === 'google' && profile?.email) {
        const allowedEmails = (process.env.ALLOWED_ADMIN_EMAILS || '').split(',');
        return allowedEmails.includes(profile.email);
      }
      
      return false;
    }
  },
  // Increase cookie security
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
