import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { getAllBlogPosts, BlogMetadata } from '@/lib/blog';
import { getServerSession } from "next-auth";
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// This API route handles CRUD operations for blog posts

export async function GET() {
  try {
    const posts = await getAllBlogPosts();
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json({ error: 'Failed to fetch blog posts' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and is admin
    const session = await getServerSession(authOptions);
    if (!session || !(session as any).isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get request body
    const body = await request.json();
    
    // Validate request
    if (!body.title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      );
    }
    
    // Create slug from title if not provided
    const slug = body.slug || body.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
    
    // Path to the metadata file
    const metadataPath = path.join(process.cwd(), 'content/blog-metadata/metadata.json');
    
    // Ensure directory exists
    const metadataDir = path.dirname(metadataPath);
    if (!fs.existsSync(metadataDir)) {
      fs.mkdirSync(metadataDir, { recursive: true });
    }
    
    // Get existing posts
    let posts: BlogMetadata[] = [];
    if (fs.existsSync(metadataPath)) {
      const fileContents = fs.readFileSync(metadataPath, 'utf8');
      posts = JSON.parse(fileContents);
    }
    
    // Check if slug already exists
    if (posts.some(post => post.slug === slug)) {
      return NextResponse.json(
        { error: 'A post with this slug already exists' },
        { status: 400 }
      );
    }
    
    // Ensure content directory exists
    const contentDir = path.join(process.cwd(), 'content/blog');
    if (!fs.existsSync(contentDir)) {
      fs.mkdirSync(contentDir, { recursive: true });
    }
    
    // Prepare new post metadata
    const newPost: BlogMetadata = {
      slug,
      title: body.title,
      date: body.date || new Date().toISOString().split('T')[0],
      author: body.author || 'Anonymous',
      excerpt: body.excerpt || '',
      coverImage: body.coverImage || '/placeholder.jpg',
      tags: body.tags || [],
      contentPath: `/content/blog/${slug}.mdx`
    };
    
    // Add new post to array
    posts.push(newPost);
    
    // Save metadata file
    fs.writeFileSync(metadataPath, JSON.stringify(posts, null, 2));
    
    // Create content file
    const contentPath = path.join(process.cwd(), 'content/blog', `${slug}.mdx`);
    fs.writeFileSync(contentPath, body.content || '# ' + body.title + '\n\nContent goes here...');
    
    return NextResponse.json({ success: true, post: newPost });
  } catch (error) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check if user is authenticated and is admin
    const session = await getServerSession(authOptions);
    if (!session || !(session as any).isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get request body
    const body = await request.json();
    
    // Validate request
    if (!body.slug) {
      return NextResponse.json(
        { error: 'Slug is required' },
        { status: 400 }
      );
    }
    
    // Path to the metadata file
    const metadataPath = path.join(process.cwd(), 'content/blog-metadata/metadata.json');
    
    // Check if metadata file exists
    if (!fs.existsSync(metadataPath)) {
      return NextResponse.json(
        { error: 'Blog metadata file not found' },
        { status: 404 }
      );
    }
    
    // Get existing posts
    const fileContents = fs.readFileSync(metadataPath, 'utf8');
    let posts: BlogMetadata[] = JSON.parse(fileContents);
    
    // Find post index
    const postIndex = posts.findIndex(post => post.slug === body.slug);
    
    if (postIndex === -1) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }
    
    // Update post metadata
    posts[postIndex] = {
      ...posts[postIndex],
      title: body.title || posts[postIndex].title,
      date: body.date || posts[postIndex].date,
      author: body.author || posts[postIndex].author,
      excerpt: body.excerpt !== undefined ? body.excerpt : posts[postIndex].excerpt,
      coverImage: body.coverImage || posts[postIndex].coverImage,
      tags: body.tags || posts[postIndex].tags,
    };
    
    // Save metadata file
    fs.writeFileSync(metadataPath, JSON.stringify(posts, null, 2));
    
    // Update content file if provided
    if (body.content) {
      const contentPath = path.join(process.cwd(), 'content/blog', `${body.slug}.mdx`);
      fs.writeFileSync(contentPath, body.content);
    }
    
    return NextResponse.json({ success: true, post: posts[postIndex] });
  } catch (error) {
    console.error('Error updating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check if user is authenticated and is admin
    const session = await getServerSession(authOptions);
    if (!session || !(session as any).isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get slug from query parameters
    const url = new URL(request.url);
    const slug = url.searchParams.get('slug');
    
    if (!slug) {
      return NextResponse.json(
        { error: 'Slug is required' },
        { status: 400 }
      );
    }
    
    // Path to the metadata file
    const metadataPath = path.join(process.cwd(), 'content/blog-metadata/metadata.json');
    
    // Check if metadata file exists
    if (!fs.existsSync(metadataPath)) {
      return NextResponse.json(
        { error: 'Blog metadata file not found' },
        { status: 404 }
      );
    }
    
    // Get existing posts
    const fileContents = fs.readFileSync(metadataPath, 'utf8');
    let posts: BlogMetadata[] = JSON.parse(fileContents);
    
    // Find post
    const post = posts.find(post => post.slug === slug);
    
    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }
    
    // Remove post from array
    posts = posts.filter(post => post.slug !== slug);
    
    // Save metadata file
    fs.writeFileSync(metadataPath, JSON.stringify(posts, null, 2));
    
    // Delete content file
    const contentPath = path.join(process.cwd(), 'content/blog', `${slug}.mdx`);
    if (fs.existsSync(contentPath)) {
      fs.unlinkSync(contentPath);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}
