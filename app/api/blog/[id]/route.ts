import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth"
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

// GET /api/blog/[id] - Get a specific blog post
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id
    
    // In a real app, you would fetch from database
    // const blog = await Blog.findById(id)
    
    // Mock data for example
    const blog = {
      id,
      title: 'React Hooks Explained',
      excerpt: 'A comprehensive guide to understanding React Hooks and how they improve your React applications.',
      content: 'Full content here with lots of markdown and code examples...',
      coverImage: '/images/blog/react-hooks.jpg',
      tags: ['React', 'JavaScript', 'Hooks', 'Frontend'],
      slug: 'react-hooks-explained',
      published: true,
      createdAt: '2023-05-15T10:00:00Z',
      updatedAt: '2023-05-15T10:00:00Z'
    }
    
    if (!blog) {
      return NextResponse.json({ error: 'Blog post not found' }, { status: 404 })
    }
    
    return NextResponse.json(blog)
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch blog post' }, { status: 500 })
  }
}

// PUT /api/blog/[id] - Update a blog post
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check if user is authenticated and is admin
    const session = await getServerSession(authOptions)
    if (!session || !(session as any).isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const id = params.id
    const data = await request.json()
    
    // Validate data
    if (!data.title || !data.content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 })
    }
    
    // In a real app, you would update in database
    // const blog = await Blog.findByIdAndUpdate(id, data, { new: true })
    
    // Mock response
    const updatedBlog = {
      id,
      ...data,
      updatedAt: new Date().toISOString()
    }
    
    return NextResponse.json(updatedBlog)
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update blog post' }, { status: 500 })
  }
}

// DELETE /api/blog/[id] - Delete a blog post
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check if user is authenticated and is admin
    const session = await getServerSession(authOptions)
    if (!session || !(session as any).isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const id = params.id
    
    // In a real app, you would delete from database
    // await Blog.findByIdAndDelete(id)
    
    return NextResponse.json({ message: `Blog post ${id} deleted successfully` })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to delete blog post' }, { status: 500 })
  }
}
