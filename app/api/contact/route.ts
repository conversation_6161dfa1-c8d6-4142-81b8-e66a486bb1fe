import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Form submission type
type ContactFormData = {
  name: string;
  email: string;
  subject: string;
  message: string;
};

// Configure mail transporter
const getTransporter = async () => {
  // Log environment variables (without exposing sensitive info)
  console.log('Environment variables check:');
  console.log('CONTACT_EMAIL exists:', !!process.env.CONTACT_EMAIL);
  console.log('SMTP_HOST exists:', !!process.env.SMTP_HOST);
  console.log('SMTP_USER exists:', !!process.env.SMTP_USER);
  console.log('SMTP_PASSWORD exists:', !!process.env.SMTP_PASSWORD);
  console.log('SMTP_PORT:', process.env.SMTP_PORT);
  console.log('SMTP_SECURE:', process.env.SMTP_SECURE);
  
  // Try to use real SMTP if we have the credentials
  if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASSWORD) {
    console.log('Using real SMTP configuration...');
    try {
      return nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: Number(process.env.SMTP_PORT || 587),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASSWORD,
        },
        tls: {
          // Do not fail on invalid certs
          rejectUnauthorized: false
        },
      });
    } catch (error) {
      console.error('Error creating SMTP transporter:', error);
    }
  }
  
  // Fallback to Ethereal test account
  console.log('Falling back to Ethereal test account...');
  try {
    const testAccount = await nodemailer.createTestAccount();
    console.log('Created test account:', testAccount.user);
    return nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });
  } catch (error) {
    console.error('Error creating Ethereal test account:', error);
    
    // Final fallback - mock transporter
    console.log('Using mock transporter as final fallback');
    return {
      sendMail: async (mailOptions: any) => {
        console.log('========== MOCK EMAIL WOULD BE SENT ==========');
        console.log('To:', mailOptions.to);
        console.log('From:', mailOptions.from);
        console.log('Subject:', mailOptions.subject);
        console.log('Text content:', mailOptions.text);
        console.log('========================================');
        
        return {
          messageId: 'mock-message-id-' + Date.now(),
          getTestMessageUrl: () => 'https://example.com/test-email'
        };
      }
    };
  }
};

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const formData: ContactFormData = await request.json();
    
    // Validate the form data
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Create a transporter
    const transporter = await getTransporter();
    
    // Log where we're sending the email
    console.log('Sending email to:', process.env.CONTACT_EMAIL || '<EMAIL>');
    
    // Compose email
    const mailOptions = {
      from: `"Contact Form" <<EMAIL>>`,
      to: process.env.CONTACT_EMAIL || '<EMAIL>', // Your email to receive messages
      replyTo: formData.email,
      subject: `Contact Form: ${formData.subject}`,
      text: `
        Name: ${formData.name}
        Email: ${formData.email}
        
        Message:
        ${formData.message}
      `,
      html: `
        <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>New message from your website contact form</h2>
          <p><strong>From:</strong> ${formData.name} (${formData.email})</p>
          <p><strong>Subject:</strong> ${formData.subject}</p>
          <div style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">
            <p style="white-space: pre-wrap;">${formData.message}</p>
          </div>
          <p style="color: #666; margin-top: 20px; font-size: 12px;">
            This message was sent from the contact form on your website.
          </p>
        </div>
      `,
    };
    
    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    if (info.messageId) {
      console.log('Message sent: %s', info.messageId);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully!'
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send message. Please try again later.' },
      { status: 500 }
    );
  }
}