"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function NewsletterSection() {
  const [showSubscribe, setShowSubscribe] = useState(false)
  const [email, setEmail] = useState("")
  const [subscribed, setSubscribed] = useState(false)

  // Show subscription popup after a delay - but only once per session
  useEffect(() => {
    // Check if localStorage is available (will prevent errors during SSR)
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('hasSeenSubscribePopup', 'true') // TODO: comment this for subscribe popup
      const hasSeenPopup = sessionStorage.getItem('hasSeenSubscribePopup')
      
      if (!hasSeenPopup) {
        const timer = setTimeout(() => {
          setShowSubscribe(true)
          // Mark that user has seen the popup this session
          sessionStorage.setItem('hasSeenSubscribePopup', 'true')
        }, 3000)
        
        return () => clearTimeout(timer)
      }
    }
  }, [])
  
  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setSubscribed(true)
      // If user subscribes, we should also save that to localStorage to never show again
      if (typeof window !== 'undefined') {
        localStorage.setItem('hasSubscribed', 'true')
      }
      setTimeout(() => {
        setShowSubscribe(false)
      }, 2000)
    }
  }

  return (
    <>
    {/* TODO: Subscribe to email footer, delete this line and uncomment below snippet for subscribe footer in blog section */}
      {/* <div className="mt-16 border-t pt-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="text-lg font-medium">Subscribe to our newsletter</h3>
            <p className="text-muted-foreground text-sm">Get the latest posts delivered right to your inbox</p>
          </div>
          <div className="flex gap-2 w-full sm:w-auto">
            <Input 
              type="email" 
              placeholder="Email address" 
              className="w-full sm:w-auto" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <Button onClick={handleSubscribe}>Subscribe</Button>
          </div>
        </div>
      </div> */}

      {/* Newsletter Subscription Popup */}
      {showSubscribe && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-md w-full p-6 relative">
            <button 
              onClick={() => setShowSubscribe(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="h-5 w-5" />
            </button>
            
            {!subscribed ? (
              <>
                <h3 className="text-xl font-bold mb-2">Stay Updated!</h3>
                <p className="text-muted-foreground mb-4">
                  Subscribe to our newsletter to receive the latest articles and updates directly to your inbox.
                </p>
                
                <form onSubmit={handleSubscribe} className="space-y-4">
                  <div>
                    <Input
                      type="email"
                      placeholder="Enter your email address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="w-full"
                    />
                  </div>
                  <div className="flex justify-between">
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={() => setShowSubscribe(false)}
                    >
                      No, thanks
                    </Button>
                    <Button type="submit">
                      Subscribe Now
                    </Button>
                  </div>
                </form>
              </>
            ) : (
              <div className="text-center py-6">
                <h3 className="text-xl font-bold mb-2">Thank You!</h3>
                <p className="text-muted-foreground">
                  You've successfully subscribed to our newsletter.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}