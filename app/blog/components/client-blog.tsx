"use client"

import { Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { BlogPostCard } from "@/components/blog-post-card"
import { useState } from "react"
import { BlogMetadata } from "@/lib/blog"
import NewsletterSection from "./newsletter-section"

interface ClientBlogProps {
  initialPosts: BlogMetadata[];
}

export default function ClientBlog({ initialPosts }: ClientBlogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  
  // Extract all unique tags
  const allTags = Array.from(
    new Set(initialPosts.flatMap((post) => post.tags || []))
  ).sort();

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags((prev) => (prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]))
  }

  // Filter posts based on search query and selected tags
  const filteredPosts = initialPosts.filter((post) => {
    const matchesSearch =
      searchQuery === "" ||
      post.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTags = selectedTags.length === 0 || 
      (post.tags && selectedTags.some((tag) => post.tags?.includes(tag)));

    return matchesSearch && matchesTags;
  });
  
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start gap-6 mb-6">
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Blogs</h1>
            <p className="text-muted-foreground md:text-xl max-w-2xl">Thoughts, insights, and tutorials on web development and modern technologies.</p>
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row justify-between items-start gap-6 mb-6">
          <div className="relative w-full md:w-2/3">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search articles..."
              className="w-full pl-8 bg-background"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          {allTags.length > 0 && (
            <div className="w-full md:w-1/3">
              <div className="flex flex-col gap-2 bg-muted/50 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  <span className="text-sm font-medium">Filter by tags</span>
                </div>
                <div className="flex flex-wrap gap-2 mt-1">
                  {allTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => toggleTag(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {filteredPosts.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredPosts.map((post, index) => (
            <BlogPostCard
              key={index}
              title={post.title || "Untitled Post"}
              excerpt={post.excerpt || "No description available"}
              date={post.date || "No date"}
              tags={post.tags || []}
              slug={post.slug}
              coverImage={post.coverImage}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border border-muted rounded-lg">
          <h3 className="text-lg font-medium mb-2">No posts found</h3>
          <p className="text-muted-foreground mb-4">
            {initialPosts.length === 0 
              ? "No blog posts have been added yet. Add .mdx files to the content/blog directory." 
              : "Try adjusting your search or filter criteria"}
          </p>
          {initialPosts.length > 0 && (
            <Button
              variant="outline"
              className="mt-2 hover:bg-muted transition-colors duration-200"
              onClick={() => {
                setSearchQuery("");
                setSelectedTags([]);
              }}
            >
              Clear filters
            </Button>
          )}
        </div>
      )}
      
      <NewsletterSection />
    </div>
  );
}