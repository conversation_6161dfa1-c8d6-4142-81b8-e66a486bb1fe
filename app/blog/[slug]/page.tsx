import { Calendar } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { notFound } from "next/navigation"
import Link from "next/link"
import { getBlogPostBySlug, getAllBlogPosts } from "@/lib/blog"

export async function generateStaticParams() {
  try {
    const posts = await getAllBlogPosts();
    return posts.map((post) => ({
      slug: post.slug,
    }));
  } catch (error) {
    console.error("Error generating static params:", error);
    return [];
  }
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  try {
    // Need to await params in Next.js 14+
    const resolvedParams = await params;
    const slug = resolvedParams.slug;
    
    const post = await getBlogPostBySlug(slug);

    if (!post) {
      notFound();
    }

    // Get recommended posts (excluding current post)
    const allPosts = await getAllBlogPosts();
    const recommendedPosts = allPosts
      .filter(p => p.slug !== post.slug)
      .slice(0, 3);

    return (
      <div className="max-w-4xl lg:max-w-5xl mx-auto px-4 sm:px-6 py-12 w-full">
        {/* Header */}
        <div className="mb-10">
          <h1 className="text-3xl font-bold mb-6 md:text-4xl lg:text-5xl">{post.title}</h1>
          
          <div className="flex flex-wrap items-center mb-6">
            <span className="font-medium mr-2">{post.author || 'Anonymous'}</span>
            <span className="mx-2 text-gray-400">·</span>
            <div className="flex items-center text-gray-500">
              <Calendar className="mr-1 h-4 w-4" />
              <span className="mr-2">{post.date}</span>
            </div>
            <span className="mx-2 text-gray-400">·</span>
            <span className="text-gray-500">{post.readingTime}</span>
          </div>
          
          <div className="flex flex-wrap gap-2 mb-8">
            {post.tags && post.tags.map((tag: string, i: number) => (
              <Badge key={i} variant="outline" className="text-sm">
                {tag}
              </Badge>
            ))}
          </div>

          {/* Featured image */}
          {post.coverImage && (
            <div className="mb-10 rounded-lg overflow-hidden">
              <img
                src={post.coverImage}
                alt={post.title}
                className="w-full object-cover max-h-[400px]"
              />
            </div>
          )}
        </div>

        {/* Article content */}
        <article className="prose prose-lg max-w-none dark:prose-invert">
          {post.content}
        </article>

        {/* Recommended Posts Section */}
        {recommendedPosts.length > 0 && (
          <div className="mt-16 border-t pt-12">
            <h2 className="text-2xl font-bold mb-8">More Articles</h2>
            <div className="grid gap-8 md:grid-cols-3">
              {recommendedPosts.map((rPost, index) => (
                <Link href={`/blog/${rPost.slug}`} key={index} className="block group">
                  <div className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                    {rPost.coverImage && (
                      <div className="aspect-video overflow-hidden">
                        <img
                          src={rPost.coverImage}
                          alt={rPost.title}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                      </div>
                    )}
                    <div className="p-5">
                      <h3 className="font-bold text-lg mb-2 group-hover:text-blue-600 transition-colors">{rPost.title}</h3>
                      <div className="flex items-center text-sm text-gray-500 mb-3">
                        <span>{rPost.author || 'Anonymous'}</span>
                        <span className="mx-2">·</span>
                        <span>{rPost.date}</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {rPost.tags && rPost.tags.slice(0, 2).map((tag: string, i: number) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error("Error rendering blog post:", error);
    notFound();
  }
}