import { ExperienceSection } from "@/components/experience-section"
import { AboutSection } from "@/components/about-section"

export default function AboutPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <AboutSection 
        name="<PERSON><PERSON><PERSON>"
        title="Software Engineer"
        bio="Versatile Software Engineer with 4+ years of experience specializing in AI Applications, backend and blockchain development. Demonstrated expertise in designing scalable systems, optimizing smart contracts, and implementing machine learning solutions."
        resumeLink="https://docs.google.com/document/d/1sbPTrh_LpgsQY1_InBljSNIDr6sWo5Q1S75Q6rIJZ6Y/edit?tab=t.0"
        showResume={true}
        workExperience={[
          {
            company: "Rapid Innovation",
            position: "Lead Engineer",
            duration: "April 2023 - Present",
            location: "Noida, India",
            description: "Leading development of microservices architectures and AI-driven solutions for enterprise clients.",
            achievements: [
              "Architected and deployed microservices based systems using Node.js, Next.js, React.js, PostgreSQL, MongoDB, and Web3.js",
              "Designed the RAG pipeline using Pinecone and LLM agents, reducing manual effort and increasing productivity by 75%",
              "Led fine-tuning and reinforcement learning (SFT, GRPO) for LLM, boosting downstream model performance",
              "Integrated GPT architectures and Mixture of Experts (MoE) scaling strategies; built end-to-end data pipelines to support efficient training and knowledge distillation of large language models"
            ],
            technologies: ["Node.js", "Next.js", "React", "PostgreSQL", "MongoDB", "Web3.js", "GPT", "LLM", "Pinecone", "GRPO"]
          },
          {
            company: "Rapid Innovation",
            position: "Software Developer",
            duration: "September 2022 - March 2023",
            location: "Noida, India",
            description: "Developed scalable backend systems and blockchain solutions for various client projects.",
            achievements: [
              "Led the development of a scalable backend for a car rental service using NestJS, optimizing database queries and implementing efficient caching strategies, which resulted in a 30% increase in booking efficiency",
              "Designed and deployed secure, gas-optimized smart contracts for EVM-compatible blockchains",
              "Mentored interns and conducted code reviews for junior developers"
            ],
            technologies: ["NestJS", "EVM", "Smart Contracts", "Solidity", "Node.js", "PostgreSQL", "Redis"]
          }
        ]}
        education={[
          {
            institution: "Krishna Engineering College",
            degree: "B.Tech in Computer Science and Engineering",
            duration: "2017 - 2021",
            location: "Ghaziabad, India",
            description: "Graduated with 79.50%. Focused on software engineering and distributed systems."
          }
        ]}
      />

      <ExperienceSection 
        experienceYears={4}
        testimonials={[
          {
            name: "Raj Malhotra",
            company: "Rapid Innovation",
            image: "/placeholder-user.jpg",
            text: "Kartik's work on our LLM pipeline significantly improved productivity. His deep understanding of AI architectures and practical implementation skills have been invaluable for our most challenging projects.",
            rating: 5
          },
          {
            name: "Priya Shah",
            company: "Product Management",
            image: "/placeholder-user.jpg",
            text: "The microservices architecture Kartik designed for our car rental platform exceeded expectations. His attention to detail and performance optimization resulted in a system that's both scalable and efficient.",
            rating: 5
          },
          {
            name: "Vikram Mehta",
            company: "Engineering Lead",
            image: "/placeholder-user.jpg",
            text: "Kartik's expertise in smart contract development was crucial for our blockchain initiatives. His gas-optimized solutions and security-first approach gave us confidence in deploying mission-critical contracts.",
            rating: 5
          },
          {
            name: "Neha Gupta",
            company: "AI Team Lead",
            image: "/placeholder-user.jpg",
            text: "The RAG implementation Kartik designed has transformed how we leverage our knowledge base. His innovative approach to fine-tuning and knowledge distillation produced results that exceeded our benchmarks.",
            rating: 5
          },
          {
            name: "Ankit Sharma",
            company: "CTO",
            image: "/placeholder-user.jpg",
            text: "Kartik's versatility across backend, blockchain, and AI domains makes him an exceptional asset. His ability to mentor junior developers while delivering complex technical solutions has accelerated our innovation roadmap.",
            rating: 5
          }
        ]}
        companies={[
          {
            name: "Rapid Innovation",
            logo: "/placeholder-logo.svg",
            period: "2022 - Present",
            role: "Lead Engineer"
          },
          {
            name: "AI & LLM Team",
            logo: "/placeholder-logo.svg",
            period: "2023 - Present",
            role: "Team Lead"
          },
          {
            name: "Blockchain Division",
            logo: "/placeholder-logo.svg",
            period: "2022 - Present",
            role: "Smart Contract Developer"
          },
          {
            name: "Microservices Team",
            logo: "/placeholder-logo.svg",
            period: "2022 - Present",
            role: "Backend Architect"
          },
          {
            name: "RAG Implementation",
            logo: "/placeholder-logo.svg",
            period: "2023 - Present",
            role: "AI Engineer"
          },
          {
            name: "Car Rental Platform",
            logo: "/placeholder-logo.svg",
            period: "2022 - 2023",
            role: "Lead Developer"
          }
        ]}
      />
    </div>
  )
}