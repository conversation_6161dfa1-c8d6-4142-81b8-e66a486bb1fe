import { Skeleton } from "@/components/ui/skeleton"

export default function AboutLoading() {
  return (
    <div className="flex flex-col min-h-screen">
      <div className="w-full py-28 md:py-32 lg:py-36 bg-muted/5 border-b border-muted/30">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-16">
            <div className="space-y-4 max-w-3xl">
              <Skeleton className="h-8 w-24 mx-auto" />
              <Skeleton className="h-10 w-64 mx-auto" />
              <Skeleton className="h-20 w-full mx-auto" />
            </div>
          </div>
          
          <div className="w-full max-w-4xl mx-auto">
            <Skeleton className="h-10 w-full mb-12" />
            <div className="space-y-8">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-60 w-full" />
              ))}
            </div>
          </div>
        </div>
      </div>
      
      <div className="w-full py-28 md:py-32 lg:py-36 bg-muted/10 border-y border-muted/30">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-16">
            <div className="space-y-4 max-w-3xl">
              <Skeleton className="h-8 w-24 mx-auto" />
              <Skeleton className="h-10 w-64 mx-auto" />
              <Skeleton className="h-20 w-full mx-auto" />
            </div>
          </div>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-24">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-40 w-full" />
            ))}
          </div>
          
          <Skeleton className="h-10 w-48 mb-8" />
          <div className="flex overflow-x-auto space-x-6 pb-6">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="flex-none w-full sm:w-[calc(100%-40px)] md:w-[calc(50%-20px)] lg:w-[calc(33.333%-20px)] h-60" />
            ))}
          </div>
          
          <Skeleton className="h-10 w-64 mt-16 mb-8" />
          <div className="flex overflow-x-auto space-x-6 pb-6">
            {[...Array(6)].map((_, i) => (
              <Skeleton key={i} className="flex-none w-[250px] h-40" />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}