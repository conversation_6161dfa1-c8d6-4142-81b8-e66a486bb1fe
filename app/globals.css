@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    
    /* Chart colors */
    --chart-1: 210 80% 60%;
    --chart-2: 160 80% 60%;
    --chart-3: 340 80% 60%;
    --chart-4: 40 80% 60%;
    --chart-5: 270 80% 60%;
    
    /* Gradient and glow variables */
    --glow-rgb: 79, 70, 229;
    --gradient-start: 79, 70, 229;
    --gradient-end: 236, 72, 153;
  }

  .dark {
    --background: 220 10% 4%;
    --foreground: 213 31% 91%;
    --card: 224 12% 6%;
    --card-foreground: 213 31% 91%;
    --popover: 224 12% 6%;
    --popover-foreground: 213 31% 91%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217 19% 12%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 19% 12%;
    --muted-foreground: 215 20% 65%;
    --accent: 261 35% 18%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 19% 15%;
    --input: 217 19% 15%;
    --ring: 262 83% 58%;
    
    /* Chart colors - vibrant in dark mode */
    --chart-1: 210 90% 60%;
    --chart-2: 160 90% 60%;
    --chart-3: 340 90% 60%;
    --chart-4: 40 90% 60%;
    --chart-5: 270 90% 60%;
    
    /* Gradient and glow variables - more vibrant in dark */
    --glow-rgb: 139, 92, 246;
    --gradient-start: 139, 92, 246;
    --gradient-end: 236, 72, 153;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Fade animations */
  .animate-fade-up {
    animation: fade-up 0.5s ease-out forwards;
    opacity: 0;
  }

  .animate-fade-down {
    animation: fade-down 0.5s ease-out forwards;
    opacity: 0;
  }

  .animate-fade-left {
    animation: fade-left 0.5s ease-out forwards;
    opacity: 0;
  }

  .animate-fade-right {
    animation: fade-right 0.5s ease-out forwards;
    opacity: 0;
  }

  /* General animations */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce {
    animation: bounce 2s infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-spin-slow {
    animation: spin 8s linear infinite;
  }

  .animate-waving {
    animation: waving 2.5s ease infinite;
    transform-origin: 70% 70%;
  }

  /* Interactive effects */
  .text-glow {
    text-shadow: 0 0 10px rgba(var(--glow-rgb), 0.5), 
                 0 0 20px rgba(var(--glow-rgb), 0.3), 
                 0 0 30px rgba(var(--glow-rgb), 0.1);
    transition: text-shadow 0.3s ease;
  }
  
  .text-glow:hover {
    text-shadow: 0 0 15px rgba(var(--glow-rgb), 0.8), 
                 0 0 30px rgba(var(--glow-rgb), 0.5), 
                 0 0 45px rgba(var(--glow-rgb), 0.3);
  }

  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-8px);
  }

  .gradient-text {
    background: linear-gradient(
      135deg, 
      rgb(var(--gradient-start)) 0%, 
      rgb(var(--gradient-end)) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .gradient-border {
    position: relative;
    border-radius: var(--radius);
  }
  
  .gradient-border::before {
    content: "";
    position: absolute;
    inset: -2px;
    border-radius: calc(var(--radius) + 2px);
    padding: 2px;
    background: linear-gradient(
      135deg, 
      rgb(var(--gradient-start)) 0%, 
      rgb(var(--gradient-end)) 100%
    );
    -webkit-mask: 
      linear-gradient(#fff 0 0) content-box, 
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  /* Keyframes for existing animations */
  @keyframes fade-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-down {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-left {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-right {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
      transform: translateY(-10px);
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-15px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes waving {
    0% { transform: rotate(0deg); }
    10% { transform: rotate(14deg); }
    20% { transform: rotate(-8deg); }
    30% { transform: rotate(14deg); }
    40% { transform: rotate(-4deg); }
    50% { transform: rotate(10deg); }
    60% { transform: rotate(0deg); }
    100% { transform: rotate(0deg); }
  }

  /* Cursor blink for typewriter */
  @keyframes blink {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }
}