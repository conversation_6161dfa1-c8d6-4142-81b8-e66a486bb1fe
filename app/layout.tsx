import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Link from "next/link"
import { Github, Twitter, Linkedin, Instagram } from "lucide-react"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/components/auth-provider"
import { Toaster } from "@/components/ui/toaster"
import { SiteHeader } from "@/components/site-header"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON> | Blockend & AI Developer",
  description: "Explore the portfolio showcasing blogs and portfolio",
  generator: '<PERSON><PERSON><PERSON>',
  icons: {
    icon: '/logo.png'
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* Umami Script for analytics */}
      <head>
        <script defer src="https://cloud.umami.is/script.js" data-website-id="d7ca511b-3688-4496-b06e-e802f3aa06a0"></script>
      </head>
      <body className={inter.className}>
          <ThemeProvider 
            attribute="class" 
            defaultTheme="dark" 
            enableSystem 
            disableTransitionOnChange
          >
          <AuthProvider>
            <div className="flex min-h-screen flex-col">
              {/* Import the client component header with mobile burger menu */}
              <SiteHeader />
              
              <main className="flex-1">{children}</main>
              
              <footer className="w-full border-t border-white/10 py-6 glass-effect">
                <div className="container flex flex-col items-center justify-between gap-6 md:h-24 md:flex-row">
                  <p className="text-sm leading-loose text-muted-foreground text-center md:text-left">
                    © 2025 <span className="gradient-text font-semibold">Kartik Jain</span>. All rights reserved.
                  </p>
                  <div className="flex flex-wrap justify-center gap-4 md:gap-6">
                    <Link href="https://github.com/kartikjain-42" className="group flex items-center gap-2 text-sm font-medium transition-all hover-lift">
                      <div className="p-2 rounded-full bg-secondary/30 group-hover:bg-secondary/50 transition-all">
                        <Github className="h-4 w-4 text-primary" />
                      </div>
                      <span className="text-glow group-hover:text-primary transition-colors hidden md:inline">GitHub</span>
                    </Link>
                    <Link href="https://www.x.com/kartikjain42" className="group flex items-center gap-2 text-sm font-medium transition-all hover-lift">
                      <div className="p-2 rounded-full bg-secondary/30 group-hover:bg-secondary/50 transition-all">
                        <Twitter className="h-4 w-4 text-primary" />
                      </div>
                      <span className="text-glow group-hover:text-primary transition-colors hidden md:inline">Twitter</span>
                    </Link>
                    <Link href="https://www.instagram.com/kartikjain_42" className="group flex items-center gap-2 text-sm font-medium transition-all hover-lift">
                      <div className="p-2 rounded-full bg-secondary/30 group-hover:bg-secondary/50 transition-all">
                        <Instagram className="h-4 w-4 text-primary" />
                      </div>
                      <span className="text-glow group-hover:text-primary transition-colors hidden md:inline">Instagram</span>
                    </Link>
                    <Link href="https://linkedin.com/in/kartikjain42" className="group flex items-center gap-2 text-sm font-medium transition-all hover-lift">
                      <div className="p-2 rounded-full bg-secondary/30 group-hover:bg-secondary/50 transition-all">
                        <Linkedin className="h-4 w-4 text-primary" />
                      </div>
                      <span className="text-glow group-hover:text-primary transition-colors hidden md:inline">LinkedIn</span>
                    </Link>
                  </div>
                </div>
              </footer>
              <Toaster />
            </div>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}