import Link from "next/link"
import { <PERSON>R<PERSON>, ExternalLink, Github, Instagram, Linkedin, Twitter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ProjectCard } from "@/components/project-card"
import { BlogPostCard } from "@/components/blog-post-card"
import { TechStack } from "@/components/tech-stack"
import { AnimatedSection } from "@/components/animated-section"
import { TypeWriter } from "@/components/type-writer"
import { ExperienceSection } from "@/components/experience-section"
import { AboutSection } from "@/components/about-section"
import { getAllBlogPosts } from "@/lib/blog"
import { getAllProjects, getFeaturedProjects } from "@/lib/project"

export default async function Home() {
  // Get blog posts and projects for the homepage
  const blogPosts = await getAllBlogPosts();
  const projects = await getAllProjects();
  
  // Array of phrases to cycle through in the typewriter effect
  const phrases = [
    "I build backend systems that scale.",
    "I create blockchain solutions that work.",
    "I develop AI models that make sense.",
    "I design efficient and automated systems."
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <AnimatedSection className="w-full py-28 md:py-36 lg:py-44 bg-gradient-to-br from-muted/30 via-background to-muted/60">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="grid gap-16 lg:grid-cols-[1fr_450px] lg:gap-24 xl:grid-cols-[1fr_600px] items-center">
            <div className="flex flex-col justify-center space-y-10">
              <div className="space-y-6">
                <div className="flex items-center gap-2 animate-fade-up">
                  <div className="h-1 w-12 bg-primary rounded-full"></div>
                  <span className="text-sm font-medium uppercase tracking-wider text-muted-foreground">Software Engineer</span>
                </div>
                <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none animate-fade-up">
                  Hi, I'm <span className="text-primary relative inline-block">
                    Kartik Jain
                    <span className="absolute -bottom-2 left-0 h-1 w-full bg-primary/30 rounded-full"></span>
                  </span>
                </h1>
                
                {/* Static tagline */}
                <p 
                  className="text-xl text-foreground font-medium animate-fade-up"
                  style={{ animationDelay: "200ms" }}
                >
                  Developer focused on Backend, Blockchain and AI
                </p>
                
                {/* Typewriter effect */}
                <div 
                  className="h-24 md:h-20 flex items-start animate-fade-up" 
                  style={{ animationDelay: "300ms" }}
                >
                  <TypeWriter phrases={phrases} typingSpeed={100} deletingSpeed={60} delayBetween={2500} />
                </div>
                
                <p 
                  className="max-w-[600px] text-muted-foreground md:text-xl animate-fade-up leading-relaxed"
                  style={{ animationDelay: "400ms" }}
                >
                  Focused on efficiency, automation, and solving real problems with tech that actually delivers.
                </p>
              </div>
              
              <div className="space-y-8 animate-fade-up" style={{ animationDelay: "500ms" }}>
                <div className="flex flex-col gap-5 sm:flex-row pt-2">
                  <Button className="relative overflow-hidden group transition-all duration-300 hover:scale-105" size="lg">
                    <Link href="/projects" className="flex items-center gap-2">
                      View My Work
                      <ArrowRight className="h-4 w-4 transform transition-transform duration-300 group-hover:translate-x-1" />
                    </Link>
                    <span className="absolute -bottom-0 left-0 h-1 w-0 bg-white/20 transition-all duration-300 group-hover:w-full"></span>
                  </Button>
                </div>
              </div>
              
              <div 
                className="flex items-center gap-6 pt-4 animate-fade-up" 
                style={{ animationDelay: "600ms" }}
              >
                <a href="https://github.com/kartikjain-42" className="text-muted-foreground hover:text-foreground transition-colors p-2 rounded-full hover:bg-muted/50 transform hover:scale-110 transition-transform duration-300">
                  <Github className="h-6 w-6" />
                  <span className="sr-only">GitHub</span>
                </a>
                <a href="https://linkedin.com/in/kartikjain42" className="text-muted-foreground hover:text-foreground transition-colors p-2 rounded-full hover:bg-muted/50 transform hover:scale-110 transition-transform duration-300">
                  <Linkedin className="h-6 w-6" />
                  <span className="sr-only">LinkedIn</span>
                </a>
                <a href="https://twitter.com/kartikjain42" className="text-muted-foreground hover:text-foreground transition-colors p-2 rounded-full hover:bg-muted/50 transform hover:scale-110 transition-transform duration-300">
                  <Twitter className="h-6 w-6" />
                  <span className="sr-only">Twitter</span>
                </a>
                <a href="https://instagram.com/kartikjain_42" className="text-muted-foreground hover:text-foreground transition-colors p-2 rounded-full hover:bg-muted/50 transform hover:scale-110 transition-transform duration-300">
                  <Instagram className="h-6 w-6" />
                  <span className="sr-only">Instagram</span>
                </a>
              </div>
            </div>
            
            <div className="relative animate-fade-left" style={{ animationDelay: "300ms" }}>
              {/* Background glows */}
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-primary/10 rounded-full blur-3xl opacity-70 animate-pulse"></div>
              <div className="absolute -bottom-12 -right-12 w-56 h-56 bg-primary/10 rounded-full blur-3xl opacity-70 animate-pulse" style={{ animationDuration: "7s" }}></div>
              
              <div className="relative z-10 bg-gradient-to-br from-background to-muted p-2 rounded-2xl shadow-xl backdrop-blur-sm border border-muted/20 hover:shadow-2xl hover:border-primary/30 transition-all duration-500">
                <img
                  src="/heroImage.png?height=550&width=450"
                  alt="Kartik Jain - Developer Portrait"
                  width={450}
                  height={550}
                  className="rounded-xl object-cover w-full aspect-[3/4] shadow-inner"
                />
                
                <div className="absolute -bottom-5 -right-5 bg-background border border-border p-3 rounded-lg shadow-lg animate-bounce" style={{ animationDuration: "3s" }}>
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">Open To Work</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AnimatedSection>


      {/* Tech Stack Section */}
      <AnimatedSection className="w-full py-28 md:py-32 lg:py-36 border-b border-muted/30">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-20">
            <div className="space-y-4 max-w-3xl">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm animate-fade-up">My Skills</div>
              <h2
                className="text-3xl font-bold tracking-tighter md:text-4xl animate-fade-up"
                style={{ animationDelay: "100ms" }}
              >
                Tech Stack
              </h2>
              <p
                className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-up"
                style={{ animationDelay: "200ms" }}
              >
                These are the technologies I work with to bring ideas to life
              </p>
            </div>
          </div>
          <div
            className="mx-auto max-w-full items-center gap-6 py-8 animate-fade-up"
            style={{ animationDelay: "300ms" }}
          >
            <TechStack />
          </div>
        </div>
      </AnimatedSection>

      {/* Projects Section */}
      <AnimatedSection className="w-full py-28 md:py-32 lg:py-36 bg-muted/5">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-20">
            <div className="space-y-4 max-w-3xl">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm animate-fade-up">Portfolio</div>
              <h2
                className="text-3xl font-bold tracking-tighter md:text-4xl animate-fade-up"
                style={{ animationDelay: "100ms" }}
              >
                Recent Projects
              </h2>
              <p
                className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-up"
                style={{ animationDelay: "200ms" }}
              >
                Check out some of my latest work
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-6xl items-center gap-10 py-8 md:grid-cols-2 lg:grid-cols-2 lg:gap-16">
            {projects.slice(0, 4).map((project, index) => (
              <div 
                key={project.slug}
                className="animate-fade-up transform hover:scale-105 transition-all duration-300" 
                style={{ animationDelay: `${300 + (index * 100)}ms` }}
              >
                <ProjectCard
                  title={project.title}
                  description={project.description}
                  tags={project.tags}
                  image={project.image}
                  link={`/projects/${project.slug}`}
                  featured={project.featured}
                />
              </div>
            ))}
            {projects.length === 0 && (
              <div className="col-span-2 text-center py-10">
                <p className="text-muted-foreground">No projects found.</p>
              </div>
            )}
          </div>
          <div className="flex justify-center mt-16 animate-fade-up" style={{ animationDelay: "700ms" }}>
            <Button asChild variant="outline" size="lg" className="gap-1 group hover:bg-primary/10 transition-all duration-300">
              <Link href="/projects">
                View All Projects
                <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>
      </AnimatedSection>

      {/* Blog Section */}
      <AnimatedSection className="w-full py-28 md:py-32 lg:py-36 border-t border-muted/30">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-20">
            <div className="space-y-4 max-w-3xl">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm animate-fade-up">Latest</div>
              <h2
                className="text-3xl font-bold tracking-tighter md:text-4xl animate-fade-up"
                style={{ animationDelay: "100ms" }}
              >
                From The Blog
              </h2>
              <p
                className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-up"
                style={{ animationDelay: "200ms" }}
              >
                Thoughts, insights, and tutorials on web development
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-6xl items-center gap-10 py-8 md:grid-cols-2 lg:grid-cols-3">
            {blogPosts.slice(0, 3).map((post, index) => (
              <div 
                key={post.slug}
                className="animate-fade-up transform hover:scale-105 transition-all duration-300" 
                style={{ animationDelay: `${300 + (index * 100)}ms` }}
              >
                <BlogPostCard
                  title={post.title}
                  excerpt={post.excerpt || ""}
                  date={post.date}
                  tags={post.tags}
                  slug={post.slug}
                  coverImage={post.coverImage}
                />
              </div>
            ))}
            {blogPosts.length === 0 && (
              <div className="col-span-3 text-center py-10">
                <p className="text-muted-foreground">No blog posts found.</p>
              </div>
            )}
          </div>
          <div className="flex justify-center mt-16 animate-fade-up" style={{ animationDelay: "600ms" }}>
            <Button asChild variant="outline" size="lg" className="gap-1 group hover:bg-primary/10 transition-all duration-300">
              <Link href="/blog">
                Read All Posts
                <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
            </Button>
          </div>
        </div>
      </AnimatedSection>


      {/* CTA Section */}
      {/* TODO: Comment out to disable CTA on home page */}
      <AnimatedSection className="w-full py-28 md:py-32 lg:py-36 bg-gradient-to-br from-primary/5 to-muted/30">
        <div className="container px-4 md:px-6 mx-auto">
          <div className="flex flex-col items-center justify-center space-y-10 text-center max-w-3xl mx-auto">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold tracking-tighter md:text-4xl animate-fade-up">Let's Work Together</h2>
              <p
                className="text-muted-foreground md:text-xl/relaxed animate-fade-up"
                style={{ animationDelay: "100ms" }}
              >
                Have a project in mind? Let's discuss how I can help bring your ideas to life.
              </p>
            </div>
            <div className="w-full max-w-sm animate-fade-up mt-6" style={{ animationDelay: "200ms" }}>
              <Button asChild size="lg" className="w-full transform transition-transform duration-300 hover:scale-105">
                <Link href="/contact">Get in Touch</Link>
              </Button>
            </div>
          </div>
        </div>
      </AnimatedSection>
    </div>
  )
}