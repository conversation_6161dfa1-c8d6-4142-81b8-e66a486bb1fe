import React from "react"
import Link from "next/link"
import { <PERSON>L<PERSON>, Gith<PERSON>, ArrowUpRight } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { notFound } from "next/navigation"
import { getProjectBySlug, getRelatedProjects } from "@/lib/project"

// This ensures static site generation for all projects
export async function generateStaticParams() {
  // Normally you would get all projects, but for simplicity we'll use specific slugs
  return [
    { slug: "ecommerce-platform" },
    { slug: "task-management-app" },
  ]
}

export default async function ProjectPage({ params, searchParams }: { 
  params: { slug: string },
  searchParams: { size?: string }
}) {
  // Await params and searchParams before using them
  const resolvedParams = await Promise.resolve(params);
  const resolvedSearchParams = await Promise.resolve(searchParams);
  
  const project = await getProjectBySlug(resolvedParams.slug)

  if (!project) {
    notFound()
  }

  // Get related projects
  const relatedProjects = await getRelatedProjects(resolvedParams.slug)
  
  // Get the image size from URL search params
  const imageSize = resolvedSearchParams.size || 'medium'
  
  return (
    <div className="container px-8 md:px-12 py-8">
      {/* Header with left border accent */}
      <div className="border-l-4 border-primary pl-6 mb-8">
        <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl">{project.title}</h1>
        <p className="text-lg text-muted-foreground mt-2">{project.description}</p>
        <div className="flex flex-wrap gap-2 mt-3">
          {project.tags.map((tag, i) => (
            <Badge key={i} variant="secondary" className="text-sm px-2.5 py-0.5">
              {tag}
            </Badge>
          ))}
        </div>
      </div>

      {/* Main content in a more compact layout */}
      <div className="grid grid-cols-1 gap-8">
        {/* Project image banner with size variants */}
        <div className="w-full rounded-lg overflow-hidden border shadow-sm">
          <div className="relative">
            {/* Image with dynamic sizing based on URL parameter */}
            <img
              src={project.image || "/placeholder.svg"}
              alt={project.title}
              className="w-full object-cover object-center"
              style={{
                height: imageSize === 'small' ? '250px' : 
                       imageSize === 'large' ? '500px' : '350px',
                maxHeight: '70vh'
              }}
            />

            {/* Size controls as visible links */}
            <div className="absolute bottom-4 right-4 bg-white/90 dark:bg-gray-800/90 rounded-lg shadow-md p-2 flex items-center gap-3 text-xs">
              <span className="font-medium mr-1">Size:</span>
              <Link 
                href={`/projects/${project.slug}?size=small`}
                className={`flex items-center gap-1 px-2 py-1 rounded ${imageSize === 'small' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                prefetch={false}
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="5" y="5" width="14" height="14" rx="2" ry="2"></rect>
                </svg>
                Small
              </Link>
              <Link 
                href={`/projects/${project.slug}?size=medium`}
                className={`flex items-center gap-1 px-2 py-1 rounded ${imageSize === 'medium' || !imageSize ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                prefetch={false}
              >
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                </svg>
                Medium
              </Link>
              <Link 
                href={`/projects/${project.slug}?size=large`}
                className={`flex items-center gap-1 px-2 py-1 rounded ${imageSize === 'large' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                prefetch={false}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                </svg>
                Large
              </Link>
            </div>
          </div>
        </div>

        {/* Action buttons centered below the image */}
        <div className="flex flex-wrap justify-center gap-4 -mt-4">
          {project.demoUrl ? (
            <Button 
              asChild={project.showDemo ?? true} 
              disabled={!(project.showDemo ?? true)} 
              className="px-6"
            >
              {(project.showDemo ?? true) ? (
                <a href={project.demoUrl} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Live Demo
                </a>
              ) : (
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Live Demo
                </div>
              )}
            </Button>
          ) : null}
          
          {project.githubUrl ? (
            <Button 
              variant="outline" 
              asChild={project.showGithub ?? true} 
              disabled={!(project.showGithub ?? true)} 
              className="px-6"
            >
              {(project.showGithub ?? true) ? (
                <a
                  href={project.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2"
                >
                  <Github className="h-4 w-4" />
                  View Code
                </a>
              ) : (
                <div className="flex items-center gap-2">
                  <Github className="h-4 w-4" />
                  View Code
                </div>
              )}
            </Button>
          ) : null}
        </div>

        {/* Project details with MDX content */}
        <div className="bg-muted/30 p-6 rounded-lg border mt-2">
          <div className="prose prose-stone dark:prose-invert max-w-none">
            {project.content}
          </div>
        </div>

        {/* Related Projects section */}
        {relatedProjects.length > 0 && (
          <div className="mt-8">
            <h2 className="text-2xl font-bold mb-6 text-center">Other Projects</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {relatedProjects.slice(0, 2).map((relatedProject, index) => (
                <Link href={`/projects/${relatedProject.slug}`} key={index} className="block hover:no-underline group">
                  <div className="border rounded-lg p-4 hover:shadow-md transition-shadow flex items-center gap-4 group-hover:border-primary/40">
                    <div className="h-16 w-16 bg-muted rounded-md flex-shrink-0 flex items-center justify-center overflow-hidden">
                      <img 
                        src={relatedProject.image || "/placeholder.svg"} 
                        alt={relatedProject.title}
                        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
                      />
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-lg font-bold group-hover:text-primary transition-colors">{relatedProject.title}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-1">{relatedProject.description}</p>
                      <div className="flex gap-1 mt-1">
                        {relatedProject.tags.map((tag, i) => (
                          <Badge key={i} variant="outline" className="text-xs px-1 py-0">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-muted group-hover:bg-primary/10 transition-colors">
                        <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}