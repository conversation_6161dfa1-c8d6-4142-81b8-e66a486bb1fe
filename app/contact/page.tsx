"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Mail, MessageSquare, Send, User, MapPin, X } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { AnimatedSection } from "@/components/animated-section"

// Custom toast component that matches the site theme
function CustomToast({ message, type, onClose }: { message: string, type: 'success' | 'error', onClose: () => void }) {
  return (
    <div className={`fixed top-4 right-4 z-[9999] flex items-center gap-2 p-4 rounded-md shadow-lg 
                     border backdrop-blur-md transition-all duration-300 
                     ${type === 'success' 
                        ? 'bg-background/80 border-primary/30 text-foreground' 
                        : 'bg-background/80 border-destructive/30 text-foreground'} 
                     max-w-sm animate-in fade-in slide-in-from-top-5`}>
      <div className="flex flex-col gap-1">
        <h4 className={`text-sm font-semibold ${type === 'success' ? 'text-primary' : 'text-destructive'}`}>
          {type === 'success' ? 'Success!' : 'Error'}
        </h4>
        <p className="text-sm">{message}</p>
      </div>
      <button onClick={onClose} className={`ml-2 p-1 rounded-full hover:bg-muted transition-colors
                                         ${type === 'success' ? 'text-primary' : 'text-destructive'}`}>
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

export default function ContactPage() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [customToast, setCustomToast] = useState<{ message: string, type: 'success' | 'error' } | null>(null)
  
  // Automatically hide the custom toast after 5 seconds
  useEffect(() => {
    if (customToast) {
      const timer = setTimeout(() => {
        setCustomToast(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [customToast])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Send the form data to the API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message')
      }

      // Use our custom toast instead
      setCustomToast({
        message: "Message sent! Thank you for reaching out. I'll get back to you soon.",
        type: 'success'
      })

      // Reset form
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
      })
    } catch (error) {
      console.error('Error sending message:', error)
      
      // Use our custom toast for errors
      setCustomToast({
        message: "We couldn't send your message. Please try again later.",
        type: 'error'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
      {customToast && (
        <CustomToast 
          message={customToast.message} 
          type={customToast.type} 
          onClose={() => setCustomToast(null)} 
        />
      )}
      <AnimatedSection>
        <div className="flex flex-col gap-2 mb-8">
          <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Contact Me</h1>
          <p className="text-muted-foreground md:text-xl max-w-2xl">
            Have a project in mind? Let's discuss how I can help bring your ideas to life.
          </p>
        </div>
      </AnimatedSection>

      <div className="grid gap-8 md:grid-cols-2">
        <AnimatedSection className="animate-fade-right" style={{ animationDelay: "200ms" }}>
          <Card>
            <CardHeader>
              <CardTitle>Get in Touch</CardTitle>
              <CardDescription>Fill out the form below and I'll get back to you as soon as possible.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <div className="relative">
                    <User className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="name"
                      name="name"
                      placeholder="Your name"
                      className="pl-8"
                      value={formData.name}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="pl-8"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <div className="relative">
                    <MessageSquare className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="subject"
                      name="subject"
                      placeholder="What's this regarding?"
                      className="pl-8"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    name="message"
                    placeholder="Tell me about your project, timeline, and budget..."
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    required
                  />
                </div>
                <Button type="submit" className="w-full group" disabled={isSubmitting}>
                  {isSubmitting ? (
                    "Sending..."
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform" /> Send Message
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </AnimatedSection>

        <AnimatedSection className="animate-fade-left" style={{ animationDelay: "300ms" }}>
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Other Ways to Connect</CardTitle>
              <CardDescription>You can also reach me through these platforms</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 flex-grow">
              <div className="rounded-lg border p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium mb-2">Email</h3>
                <p className="text-sm text-muted-foreground mb-2">For project inquiries and collaborations</p>
                <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </a>
              </div>
              <div className="rounded-lg border p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium mb-2">Social Media</h3>
                <p className="text-sm text-muted-foreground mb-2">Connect with me on these platforms</p>
                <div className="flex gap-4">
                  <a href="https://github.com/kartikjain-42" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">
                    GitHub
                  </a>
                  <a href="https://linkedin.com/in/kartikjain42" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">
                    LinkedIn
                  </a>
                  <a href="https://twitter.com/kartikjain42" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">
                    Twitter
                  </a>
                </div>
              </div>
              <div className="rounded-lg border p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium mb-2 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Based in New Delhi, India • Available for remote work worldwide
                </p>
                <div className="aspect-video w-full h-[200px] rounded-md overflow-hidden border">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d224346.54004238244!2d77.06889754827302!3d28.52728034389693!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390cfd5b347eb62d%3A0x37205b715389640!2sNew%20Delhi%2C%20Delhi%2C%20India!5e0!3m2!1sen!2sus!4v1717667427093!5m2!1sen!2sus"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen={false}
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  ></iframe>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">Typical response time: 24-48 hours</p>
            </CardFooter>
          </Card>
        </AnimatedSection>
      </div>
    </div>
  )
}