# Personal Website

A Next.js-based personal website/portfolio with blog functionality.

## Features

- Modern UI with Tailwind CSS
- Dark/Light mode support
- Responsive design
- Blog with MDX support
- Project showcases
- Contact form with email notifications

## Blog System

The blog system uses MDX files stored in the `content/blog` directory with metadata in `content/blog-metadata/metadata.json`.

### Blog Post Format

Create your blog posts as `.mdx` files in the `content/blog` directory. Each post should have the following frontmatter:

```md
---
title: "Your Blog Post Title"
excerpt: "A short description of your blog post"
date: "YYYY-MM-DD"
author: "Your Name"
tags: ["Tag1", "Tag2", "Tag3"]
coverImage: "/images/blog/your-image.jpg"
---

# Your content goes here

Write your blog post content using Markdown.
```

Also update the corresponding entry in `content/blog-metadata/metadata.json`:

```json
{
  "slug": "your-blog-post-slug",
  "title": "Your Blog Post Title",
  "date": "YYYY-MM-DD",
  "author": "Your Name",
  "excerpt": "A short description of your blog post",
  "tags": ["Tag1", "Tag2", "Tag3"],
  "coverImage": "/images/blog/your-image.jpg",
  "contentPath": "your-blog-post-slug.mdx"
}
```

## Project System

The project system works similarly to the blog system, using MDX files in `content/projects` with metadata in `content/project-metadata/metadata.json`.

### Project Format

Create your project files as `.mdx` files in the `content/projects` directory with frontmatter:

```md
---
title: "Your Project Title"
description: "A short description of your project"
tags: ["Tag1", "Tag2", "Tag3"]
image: "/images/projects/your-image.jpg"
demoUrl: "https://example.com/demo"
githubUrl: "https://github.com/username/project"
---

Project content in Markdown format...
```

Also update the corresponding entry in `content/project-metadata/metadata.json`:

```json
{
  "slug": "your-project-slug",
  "title": "Your Project Title",
  "description": "A short description of your project",
  "tags": ["Tag1", "Tag2", "Tag3"],
  "image": "/images/projects/your-image.jpg",
  "featured": true,
  "demoUrl": "https://example.com/demo",
  "githubUrl": "https://github.com/username/project",
  "contentPath": "your-project-slug.mdx"
}
```

## Contact Form Setup

The contact form is set up to send email notifications when users submit the form. To configure it:

1. Copy `.env.local.example` to `.env.local`:
   ```bash
   cp .env.local.example .env.local
   ```

2. Update the email settings in `.env.local` with your SMTP server details:
   ```
   SMTP_HOST=your-smtp-server.com
   SMTP_PORT=587
   SMTP_SECURE=false
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-email-password
   CONTACT_EMAIL=<EMAIL>
   ```

   - For Gmail, you'll need to use an App Password instead of your regular password
   - If you don't provide SMTP settings, the form will use Ethereal (a test email service) and log the preview URL in the console

## Images

Store blog post images in the `public/images/blog` directory and project images in `public/images/projects`. Reference them in your MDX files using:

```md
![Image description](/images/blog/your-image.jpg)
```

## Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Deployment

This project can be easily deployed on Vercel or any other hosting platform that supports Next.js.

When deploying, make sure to set up the environment variables for the contact form in your hosting platform's dashboard.