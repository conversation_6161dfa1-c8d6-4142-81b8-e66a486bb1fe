[{"slug": "filegen-mcp-server", "title": "🚀 FileGen MCP Server", "description": "A Model Context Protocol server to automate file operations, project initialization, and command execution — ideal for backend workflows and LLM-based agent integrations.", "tags": ["Python", "MCP"], "image": "/images/projects/mcp.png", "featured": true, "demoUrl": "", "githubUrl": "https://github.com/kartikjain-42/filegen", "showDemo": false, "showGithub": true, "contentPath": "filegen-mcp.mdx"}]