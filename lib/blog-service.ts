// This file acts as a server-side service that doesn't directly use fs/node modules
import type { BlogPost } from '@/lib/blog';

// Get blog posts through the API route
export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    // Using relative URL to ensure it works in both development and production
    const response = await fetch('/api/blog/posts', { 
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Ensure fresh data on each request in development
      cache: process.env.NODE_ENV === 'development' ? 'no-store' : 'force-cache',
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch blog posts: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.posts || [];
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return [];
  }
}