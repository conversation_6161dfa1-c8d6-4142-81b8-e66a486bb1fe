'use server';

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { compileMDX } from 'next-mdx-remote/rsc';
import rehypeHighlight from 'rehype-highlight';

// Types
export interface ProjectMetadata {
  slug: string;
  title: string;
  description: string;
  tags: string[];
  image: string;
  featured?: boolean;
  demoUrl?: string;
  githubUrl?: string;
  showDemo?: boolean;
  showGithub?: boolean;
  contentPath: string;
}

export interface Project extends ProjectMetadata {
  content: React.ReactNode;
}

// Path to the metadata JSON file
const metadataPath = path.join(process.cwd(), 'content/project-metadata/metadata.json');

// Get all projects metadata
export async function getAllProjects(): Promise<ProjectMetadata[]> {
  try {
    // Check if metadata file exists
    if (!fs.existsSync(metadataPath)) {
      console.error('Project metadata file not found:', metadataPath);
      return [];
    }

    // Read and parse the metadata file
    const fileContents = fs.readFileSync(metadataPath, 'utf8');
    const projects = JSON.parse(fileContents) as ProjectMetadata[];

    return projects;
  } catch (error) {
    console.error('Error getting projects metadata:', error);
    return [];
  }
}

// Get a specific project by slug
export async function getProjectBySlug(slug: string): Promise<Project | null> {
  try {
    // Get all projects metadata
    const allProjects = await getAllProjects();
    
    // Find the project with the matching slug
    const projectMetadata = allProjects.find(project => project.slug === slug);
    
    if (!projectMetadata) {
      console.error(`No project found with slug: ${slug}`);
      return null;
    }
    
    // Get the content file path
    const contentPath = path.join(process.cwd(), 'content/projects', `${projectMetadata.contentPath}`);
    
    // Check if content file exists
    if (!fs.existsSync(contentPath)) {
      console.error(`Content file not found: ${contentPath}`);
      return null;
    }
    
    // Read and parse the content file
    const fileContents = fs.readFileSync(contentPath, 'utf8');
    
    // Extract content and frontmatter using gray-matter
    const { content } = matter(fileContents);
    
    // Compile MDX content
    const { content: mdxContent } = await compileMDX({
      source: content,
      options: {
        mdxOptions: {
          rehypePlugins: [rehypeHighlight],
        },
        parseFrontmatter: false,
      },
    });
    
    // Return project data
    return {
      ...projectMetadata,
      content: mdxContent,
    };
  } catch (error) {
    console.error(`Error getting project for slug ${slug}:`, error);
    return null;
  }
}

// Get featured projects
export async function getFeaturedProjects(): Promise<ProjectMetadata[]> {
  const projects = await getAllProjects();
  return projects.filter(project => project.featured);
}

// Get related projects (all except the current one)
export async function getRelatedProjects(currentSlug: string): Promise<ProjectMetadata[]> {
  const projects = await getAllProjects();
  return projects.filter(project => project.slug !== currentSlug);
}