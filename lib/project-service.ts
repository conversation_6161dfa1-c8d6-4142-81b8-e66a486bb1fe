// Client-side service for fetching project data

// Types
import { ProjectMetadata, Project } from './project';

// API endpoint to fetch all projects
export async function fetchAllProjects(): Promise<ProjectMetadata[]> {
  try {
    const response = await fetch('/api/projects', {
      // Cache the result in production, revalidate in development
      cache: process.env.NODE_ENV === 'production' ? 'force-cache' : 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch projects: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching projects:', error);
    return [];
  }
}

// API endpoint to fetch a specific project by slug
export async function fetchProjectBySlug(slug: string): Promise<Project | null> {
  try {
    const response = await fetch(`/api/projects/${slug}`, {
      // Cache the result in production, revalidate in development
      cache: process.env.NODE_ENV === 'production' ? 'force-cache' : 'no-store',
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch project: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching project with slug ${slug}:`, error);
    return null;
  }
}