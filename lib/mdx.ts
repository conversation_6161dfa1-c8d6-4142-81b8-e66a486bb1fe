'use server';

import fs from 'node:fs';
import path from 'path';
import matter from 'gray-matter';
import { compileMDX } from 'next-mdx-remote/rsc';
import rehypeHighlight from 'rehype-highlight';

// Directory where blog posts are stored
const blogDirectory = path.join(process.cwd(), 'content/blog');

// Get all blog posts
export async function getAllBlogPosts() {
  try {
    // Create directory if it doesn't exist
    if (!fs.existsSync(blogDirectory)) {
      fs.mkdirSync(blogDirectory, { recursive: true });
      console.log(`Created blog directory: ${blogDirectory}`);
      return [];
    }

    const fileNames = fs.readdirSync(blogDirectory);
    console.log(`Found ${fileNames.length} files in blog directory:`, fileNames);
    
    const allPostsData = await Promise.all(fileNames
      .filter(fileName => fileName.endsWith('.mdx') || fileName.endsWith('.md'))
      .map(async (fileName) => {
        // Remove ".mdx" or ".md" from file name to get slug
        const slug = fileName.replace(/\.(mdx|md)$/, '');
        
        try {
          // Read markdown file as string
          const fullPath = path.join(blogDirectory, fileName);
          const fileContents = fs.readFileSync(fullPath, 'utf8');
          
          // Use gray-matter to parse the post metadata section
          const { data: frontmatter } = matter(fileContents);
          
          // Ensure tags are properly formatted
          const tags = frontmatter.tags || [];
          const formattedTags = Array.isArray(tags) ? tags : tags.split(',').map((tag: string) => tag.trim());
          
          // Format date if available
          let formattedDate = '';
          if (frontmatter.date) {
            const date = new Date(frontmatter.date);
            if (!isNaN(date.getTime())) {
              formattedDate = date.toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric', 
                year: 'numeric'
              });
            } else {
              formattedDate = frontmatter.date;
            }
          }
          
          // Return combined frontmatter and slug
          return {
            slug,
            ...frontmatter,
            date: formattedDate,
            tags: formattedTags,
          };
        } catch (error) {
          console.error(`Error processing file ${fileName}:`, error);
          return null;
        }
      }));
      
    // Filter out any null values from errors and sort by date
    const validPosts = allPostsData
      .filter(Boolean)
      .sort((a, b) => {
        if (!a.date) return 1;
        if (!b.date) return -1;
        return new Date(b.date) > new Date(a.date) ? 1 : -1;
      });
      
    console.log(`Processed ${validPosts.length} valid blog posts`);
    return validPosts;
  } catch (error) {
    console.error('Error getting all blog posts:', error);
    return [];
  }
}

// Get a specific blog post by slug
export async function getBlogPostBySlug(slug: string) {
  try {
    // Make sure blog directory exists
    if (!fs.existsSync(blogDirectory)) {
      fs.mkdirSync(blogDirectory, { recursive: true });
    }
    
    // Look for both .mdx and .md extensions
    let fullPath;
    
    if (fs.existsSync(path.join(blogDirectory, `${slug}.mdx`))) {
      fullPath = path.join(blogDirectory, `${slug}.mdx`);
    } else if (fs.existsSync(path.join(blogDirectory, `${slug}.md`))) {
      fullPath = path.join(blogDirectory, `${slug}.md`);
    } else {
      console.error(`No file found for slug: ${slug}`);
      return null;
    }
    
    console.log(`Found file for slug ${slug}: ${fullPath}`);
    const fileContents = fs.readFileSync(fullPath, 'utf8');
    
    // Use gray-matter to parse the post metadata section
    const { data: frontmatter, content } = matter(fileContents);
    console.log(`Parsed frontmatter for ${slug}:`, frontmatter);
    
    // Ensure tags are properly formatted
    const tags = frontmatter.tags || [];
    const formattedTags = Array.isArray(tags) ? tags : tags.split(',').map((tag: string) => tag.trim());
    
    // Compile MDX content
    const { content: mdxContent } = await compileMDX({
      source: content,
      options: {
        mdxOptions: {
          rehypePlugins: [rehypeHighlight],
        },
        parseFrontmatter: false, // We've already parsed it with gray-matter
      },
    });
    
    // Calculate read time
    const readTimeMinutes = calculateReadTime(content);
    
    // Format date if available
    let formattedDate = '';
    if (frontmatter.date) {
      const date = new Date(frontmatter.date);
      if (!isNaN(date.getTime())) {
        formattedDate = date.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric', 
          year: 'numeric'
        });
      } else {
        formattedDate = frontmatter.date;
      }
    }
    
    // Return blog post data
    return {
      slug,
      content: mdxContent,
      readingTime: `${readTimeMinutes} min read`,
      ...frontmatter,
      date: formattedDate,
      tags: formattedTags,
    };
  } catch (error) {
    console.error(`Error getting blog post for slug ${slug}:`, error);
    return null;
  }
}

// Function to calculate reading time
function calculateReadTime(content: string): number {
  try {
    // Strip markdown formatting
    const strippedContent = content
      .replace(/```[a-z]*\n[\s\S]*?```/g, '') // Remove code blocks
      .replace(/#{1,6}\s+/g, '') // Remove headings
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Replace links with just the text
      .replace(/!\[([^\]]+)\]\([^)]+\)/g, '') // Remove images
      .replace(/`([^`]+)`/g, '$1'); // Remove inline code formatting
      
    // Count words
    const words = strippedContent.split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Calculate reading time based on average reading speed (275 words per minute)
    const readingSpeed = 275;
    const readingTimeMinutes = wordCount / readingSpeed;
    
    // Round up to the nearest minute and ensure at least 1 minute
    return Math.max(1, Math.ceil(readingTimeMinutes));
  } catch (error) {
    console.error('Error calculating reading time:', error);
    return 1; // Default to 1 minute if there's an error
  }
}