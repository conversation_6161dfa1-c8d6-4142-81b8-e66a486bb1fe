'use server';

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { compileMDX } from 'next-mdx-remote/rsc';
import rehypeHighlight from 'rehype-highlight';

// Types
export interface BlogMetadata {
  slug: string;
  title: string;
  date: string;
  author: string;
  excerpt?: string;
  coverImage?: string;
  tags: string[];
  contentPath: string;
}

export interface BlogPost extends BlogMetadata {
  content: React.ReactNode;
  readingTime: string;
}

// Path to the metadata JSON file
const metadataPath = path.join(process.cwd(), 'content/blog-metadata/metadata.json');

// Get all blog posts metadata
export async function getAllBlogPosts(): Promise<BlogMetadata[]> {
  try {
    // Check if metadata file exists
    if (!fs.existsSync(metadataPath)) {
      console.error('Blog metadata file not found:', metadataPath);
      return [];
    }

    // Read and parse the metadata file
    const fileContents = fs.readFileSync(metadataPath, 'utf8');
    const posts = JSON.parse(fileContents) as BlogMetadata[];

    // Sort by date (newest first)
    return posts.sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
  } catch (error) {
    console.error('Error getting blog posts metadata:', error);
    return [];
  }
}

// Get a specific blog post by slug
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    // Get all blog posts metadata
    const allPosts = await getAllBlogPosts();
    
    // Find the post with the matching slug
    const postMetadata = allPosts.find(post => post.slug === slug);
    
    if (!postMetadata) {
      console.error(`No post found with slug: ${slug}`);
      return null;
    }
    
    // Get the content file path
    const contentPath = path.join(process.cwd(), 'content/blog', postMetadata.contentPath);
    
    // Check if content file exists
    if (!fs.existsSync(contentPath)) {
      console.error(`Content file not found: ${contentPath}`);
      return null;
    }
    
    // Read and parse the content file
    const fileContents = fs.readFileSync(contentPath, 'utf8');
    
    // Extract content and frontmatter using gray-matter
    const { content } = matter(fileContents);
    
    // Compile MDX content
    const { content: mdxContent } = await compileMDX({
      source: content,
      options: {
        mdxOptions: {
          rehypePlugins: [rehypeHighlight],
        },
        parseFrontmatter: false,
      },
    });
    
    // Calculate reading time
    const readingTime = calculateReadTime(content);
    
    // Format date
    const formattedDate = formatDate(postMetadata.date);
    
    // Return blog post data
    return {
      ...postMetadata,
      date: formattedDate,
      content: mdxContent,
      readingTime: `${readingTime} min read`,
    };
  } catch (error) {
    console.error(`Error getting blog post for slug ${slug}:`, error);
    return null;
  }
}

// Format date
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  } catch (error) {
    return dateString;
  }
}

// Calculate reading time
function calculateReadTime(content: string): number {
  try {
    // Strip markdown formatting
    const strippedContent = content
      .replace(/```[a-z]*\n[\s\S]*?```/g, '') // Remove code blocks
      .replace(/#{1,6}\s+/g, '') // Remove headings
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Replace links with just the text
      .replace(/!\[([^\]]+)\]\([^)]+\)/g, '') // Remove images
      .replace(/`([^`]+)`/g, '$1'); // Remove inline code formatting
      
    // Count words
    const words = strippedContent.split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Calculate reading time based on average reading speed (275 words per minute)
    const readingSpeed = 275;
    const readingTimeMinutes = wordCount / readingSpeed;
    
    // Round up to the nearest minute and ensure at least 1 minute
    return Math.max(1, Math.ceil(readingTimeMinutes));
  } catch (error) {
    console.error('Error calculating reading time:', error);
    return 1; // Default to 1 minute if there's an error
  }
}